/**
 * Firefox Authentication Recovery System
 * Handles silent authentication failures and provides recovery mechanisms
 */

import { isFirefox } from './compatibilityChecker';

export class FirefoxAuthRecovery {
  private static instance: FirefoxAuthRecovery;
  private isProduction: boolean;
  private recoveryAttempts: number = 0;
  private maxRecoveryAttempts: number = 3;

  constructor() {
    this.isProduction = import.meta.env.PROD || window.location.protocol === 'https:';
  }

  public static getInstance(): FirefoxAuthRecovery {
    if (!FirefoxAuthRecovery.instance) {
      FirefoxAuthRecovery.instance = new FirefoxAuthRecovery();
    }
    return FirefoxAuthRecovery.instance;
  }

  /**
   * Detects if authentication failed silently
   */
  public detectSilentAuthFailure(authResult: any): boolean {
    if (!isFirefox()) return false;

    console.log('🔍 Checking for silent authentication failure...');
    console.log('🔍 Auth result:', authResult);

    // Check for various silent failure patterns
    const silentFailureIndicators = [
      // No error but no user data
      !authResult?.error && !authResult?.user,
      // Error is null/undefined but authentication didn't succeed
      authResult?.error === null && !authResult?.user,
      // Empty error object
      authResult?.error && Object.keys(authResult.error).length === 0,
      // Undefined result
      authResult === undefined || authResult === null
    ];

    const isSilentFailure = silentFailureIndicators.some(indicator => indicator);

    if (isSilentFailure) {
      console.log('🦊 Silent authentication failure detected');
      this.handleSilentFailure();
      return true;
    }

    return false;
  }

  /**
   * Handles silent authentication failures
   */
  private handleSilentFailure(): void {
    this.recoveryAttempts++;
    console.log(`🦊 Handling silent failure (attempt ${this.recoveryAttempts}/${this.maxRecoveryAttempts})`);

    if (this.recoveryAttempts >= this.maxRecoveryAttempts) {
      console.log('🦊 Max recovery attempts reached, forcing page reload');
      this.forcePageReload();
      return;
    }

    // Set recovery flag
    localStorage.setItem('firefox-auth-recovery-in-progress', 'true');
    localStorage.setItem('firefox-auth-recovery-attempts', this.recoveryAttempts.toString());

    // Clear all authentication-related data
    this.clearAllAuthData();

    // Trigger recovery after a short delay
    setTimeout(() => {
      this.triggerAuthRecovery();
    }, 1000);
  }

  /**
   * Clears all authentication-related data
   */
  private clearAllAuthData(): void {
    console.log('🦊 Clearing all authentication data...');

    // Clear localStorage auth keys
    const authKeys = [
      'sb-auth-token',
      'supabase.auth.token',
      'datastatpro-auth-state',
      'datastatpro-auth-in-progress',
      'firefox-auth-success',
      'firefox-sw-disabled-for-auth',
      'firefox-sw-unregistered-for-auth'
    ];

    authKeys.forEach(key => {
      localStorage.removeItem(key);
    });

    // Clear sessionStorage
    sessionStorage.removeItem('isGuest');
    sessionStorage.removeItem('showSignupSuccess');

    // Clear all caches if in production
    if (this.isProduction) {
      this.clearAllCaches();
    }

    console.log('🦊 Authentication data cleared');
  }

  /**
   * Clears all browser caches
   */
  private async clearAllCaches(): Promise<void> {
    try {
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        console.log(`🦊 Clearing ${cacheNames.length} caches...`);

        for (const cacheName of cacheNames) {
          await caches.delete(cacheName);
          console.log(`🦊 Cleared cache: ${cacheName}`);
        }
      }
    } catch (error) {
      console.warn('🦊 Cache clearing failed:', error);
    }
  }

  /**
   * Triggers authentication recovery
   */
  private triggerAuthRecovery(): void {
    console.log('🦊 Triggering authentication recovery...');

    // Dispatch a custom event to notify the application
    window.dispatchEvent(new CustomEvent('firefox-auth-recovery', {
      detail: {
        attempt: this.recoveryAttempts,
        maxAttempts: this.maxRecoveryAttempts
      }
    }));

    // If this is the final attempt, force a page reload
    if (this.recoveryAttempts >= this.maxRecoveryAttempts - 1) {
      setTimeout(() => {
        this.forcePageReload();
      }, 5000);
    }
  }

  /**
   * Forces a complete page reload
   */
  private forcePageReload(): void {
    console.log('🦊 Forcing page reload for authentication recovery...');
    
    // Clear recovery flags
    localStorage.removeItem('firefox-auth-recovery-in-progress');
    localStorage.removeItem('firefox-auth-recovery-attempts');
    
    // Set a flag to indicate forced reload
    localStorage.setItem('firefox-forced-reload', 'true');
    
    // Force reload
    window.location.reload();
  }

  /**
   * Checks if recovery is in progress
   */
  public isRecoveryInProgress(): boolean {
    return localStorage.getItem('firefox-auth-recovery-in-progress') === 'true';
  }

  /**
   * Gets recovery status
   */
  public getRecoveryStatus(): {
    inProgress: boolean;
    attempts: number;
    maxAttempts: number;
    wasForceReloaded: boolean;
  } {
    const inProgress = this.isRecoveryInProgress();
    const attempts = parseInt(localStorage.getItem('firefox-auth-recovery-attempts') || '0');
    const wasForceReloaded = localStorage.getItem('firefox-forced-reload') === 'true';

    // Clear force reload flag after reading
    if (wasForceReloaded) {
      localStorage.removeItem('firefox-forced-reload');
    }

    return {
      inProgress,
      attempts,
      maxAttempts: this.maxRecoveryAttempts,
      wasForceReloaded
    };
  }

  /**
   * Resets recovery state
   */
  public resetRecoveryState(): void {
    console.log('🦊 Resetting recovery state...');
    this.recoveryAttempts = 0;
    localStorage.removeItem('firefox-auth-recovery-in-progress');
    localStorage.removeItem('firefox-auth-recovery-attempts');
    localStorage.removeItem('firefox-forced-reload');
  }

  /**
   * Initializes recovery system
   */
  public initialize(): void {
    if (!isFirefox()) return;

    console.log('🦊 Initializing Firefox authentication recovery system...');

    // Check if we just recovered from a forced reload
    const recoveryStatus = this.getRecoveryStatus();
    if (recoveryStatus.wasForceReloaded) {
      console.log('🦊 Detected recovery from forced reload');
      this.resetRecoveryState();
    }

    // Set up recovery event listener
    window.addEventListener('firefox-auth-recovery', (event: any) => {
      console.log('🦊 Recovery event received:', event.detail);
    });

    console.log('🦊 Firefox authentication recovery system initialized');
  }

  /**
   * Gets debug information
   */
  public getDebugInfo(): object {
    return {
      isFirefox: isFirefox(),
      isProduction: this.isProduction,
      recoveryAttempts: this.recoveryAttempts,
      maxRecoveryAttempts: this.maxRecoveryAttempts,
      recoveryStatus: this.getRecoveryStatus(),
      timestamp: new Date().toISOString()
    };
  }
}

// Export singleton instance
export const firefoxAuthRecovery = FirefoxAuthRecovery.getInstance();
