import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  Alert,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  Avatar,
  Card,
  CardContent,
  CircularProgress,
  Switch,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField
} from '@mui/material';
import { useAuth } from '../context/AuthContext';
import { Person as PersonIcon, Refresh as RefreshIcon } from '@mui/icons-material';
import { firefoxAuthHandler } from '../utils/firefoxAuthHandler';
import { isFirefox, getFirefoxVersion, shouldUseFirefoxAuthFallback } from '../utils/compatibilityChecker';
import { productionFirefoxFix } from '../utils/productionFirefoxFix';
import { productionFirefoxAuthFix } from '../utils/productionFirefoxAuthFix';
import { getEarlyFirefoxDebugInfo } from '../utils/earlyFirefoxInit';
import { firefoxAuthRecovery } from '../utils/firefoxAuthRecovery';

const AuthTestPage: React.FC = () => {
  const { 
    user, 
    userProfile, 
    accountType, 
    isAuthenticated, 
    isGuest, 
    loading,
    refreshProfile,
    signOut
  } = useAuth();

  const [refreshing, setRefreshing] = useState(false);
  const [diagnosticMode, setDiagnosticMode] = useState(false);
  const [authLogs, setAuthLogs] = useState<string[]>([]);
  const [storageData, setStorageData] = useState<{[key: string]: string}>({});
  const [crossTabTest, setCrossTabTest] = useState(false);

  // Enhanced logging for diagnostics
  useEffect(() => {
    if (!diagnosticMode) return;

    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;

    const logCapture = (level: string, ...args: any[]) => {
      const message = args.map(arg =>
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
      ).join(' ');

      if (message.includes('🔄') || message.includes('✅') || message.includes('❌') ||
          message.includes('auth') || message.includes('Auth') || message.includes('logout') ||
          message.includes('signOut') || message.includes('SIGNED_OUT')) {
        const timestamp = new Date().toLocaleTimeString();
        setAuthLogs(prev => [...prev.slice(-49), `[${timestamp}] ${level.toUpperCase()}: ${message}`]);
      }
    };

    console.log = (...args) => {
      originalConsoleLog(...args);
      logCapture('log', ...args);
    };

    console.error = (...args) => {
      originalConsoleError(...args);
      logCapture('error', ...args);
    };

    console.warn = (...args) => {
      originalConsoleWarn(...args);
      logCapture('warn', ...args);
    };

    return () => {
      console.log = originalConsoleLog;
      console.error = originalConsoleError;
      console.warn = originalConsoleWarn;
    };
  }, [diagnosticMode]);

  // Monitor storage changes
  useEffect(() => {
    if (!diagnosticMode) return;

    const updateStorageData = () => {
      const data: {[key: string]: string} = {};

      // Check relevant localStorage keys
      const relevantKeys = [
        'datastatpro-auth-loading-stuck',
        'datastatpro-logout-signal',
        'datastatpro-login-signal',
        'datastatpro-cache-corruption-detected',
        'datastatpro-loading-failures'
      ];

      relevantKeys.forEach(key => {
        const value = localStorage.getItem(key);
        if (value !== null) {
          data[key] = value;
        }
      });

      // Check sessionStorage
      const sessionKeys = ['isGuest', 'showSignupSuccess'];
      sessionKeys.forEach(key => {
        const value = sessionStorage.getItem(key);
        if (value !== null) {
          data[`session:${key}`] = value;
        }
      });

      setStorageData(data);
    };

    updateStorageData();
    const interval = setInterval(updateStorageData, 1000);

    return () => clearInterval(interval);
  }, [diagnosticMode]);

  const handleRefreshProfile = async () => {
    setRefreshing(true);
    try {
      await refreshProfile();
    } finally {
      setRefreshing(false);
    }
  };

  const handleLogout = async () => {
    try {
      if (diagnosticMode) {
        console.log('🔄 [DIAGNOSTIC] Starting logout test...');
      }
      await signOut();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const clearAuthLogs = () => {
    setAuthLogs([]);
  };

  const testCrossTabLogout = () => {
    if (diagnosticMode) {
      console.log('🔄 [DIAGNOSTIC] Testing cross-tab logout signal...');
    }

    const logoutSignal = {
      timestamp: Date.now(),
      reason: 'cross-tab-test'
    };
    localStorage.setItem('datastatpro-logout-signal', JSON.stringify(logoutSignal));

    setTimeout(() => {
      localStorage.removeItem('datastatpro-logout-signal');
    }, 2000);
  };

  const testCrossTabLogin = () => {
    if (diagnosticMode) {
      console.log('🔄 [DIAGNOSTIC] Testing cross-tab login signal...');
    }

    const loginSignal = {
      timestamp: Date.now(),
      userId: user?.id || 'test-user'
    };
    localStorage.setItem('datastatpro-login-signal', JSON.stringify(loginSignal));

    setTimeout(() => {
      localStorage.removeItem('datastatpro-login-signal');
    }, 2000);
  };

  const clearAllAuthStorage = () => {
    if (diagnosticMode) {
      console.log('🔄 [DIAGNOSTIC] Clearing all auth-related storage...');
    }

    // Clear localStorage
    const authKeys = Object.keys(localStorage).filter(key =>
      key.includes('datastatpro') || key.includes('auth')
    );
    authKeys.forEach(key => localStorage.removeItem(key));

    // Clear sessionStorage
    sessionStorage.removeItem('isGuest');
    sessionStorage.removeItem('showSignupSuccess');
  };

  const simulateNetworkError = async () => {
    if (diagnosticMode) {
      console.log('🔄 [DIAGNOSTIC] Simulating network error during logout...');
    }

    // This will test the error handling in signOut
    const originalFetch = window.fetch;
    window.fetch = () => Promise.reject(new Error('Simulated network error'));

    try {
      await signOut();
    } catch (error) {
      console.error('Expected error from simulation:', error);
    } finally {
      // Restore original fetch after a delay
      setTimeout(() => {
        window.fetch = originalFetch;
      }, 1000);
    }
  };

  const testFirefoxAuthPreparation = async () => {
    if (diagnosticMode) {
      console.log('🦊 [DIAGNOSTIC] Testing Firefox auth preparation...');
    }

    try {
      // Test environment detection consistency
      const envCheck = {
        earlyFixProduction: window.location.protocol === 'https:' || (typeof import.meta !== 'undefined' && import.meta.env?.PROD),
        prodFixProduction: (typeof import.meta !== 'undefined' && import.meta.env?.PROD) || window.location.protocol === 'https:',
        protocol: window.location.protocol,
        hostname: window.location.hostname,
        importMetaEnv: typeof import.meta !== 'undefined' ? import.meta.env?.PROD : 'undefined',
        consistent: ((window.location.protocol === 'https:' || (typeof import.meta !== 'undefined' && import.meta.env?.PROD)) ===
                    ((typeof import.meta !== 'undefined' && import.meta.env?.PROD) || window.location.protocol === 'https:'))
      };
      console.log('🦊 Environment detection check:', envCheck);

      if (!envCheck.consistent) {
        console.warn('⚠️ INCONSISTENT ENVIRONMENT DETECTION - This may cause Firefox auth issues!');
      }

      await firefoxAuthHandler.prepareForAuthentication();
      console.log('✅ Firefox auth preparation completed');
    } catch (error) {
      console.error('❌ Firefox auth preparation failed:', error);
    }
  };

  const getFirefoxDebugInfo = () => {
    return firefoxAuthHandler.getDebugInfo();
  };

  const forceFirefoxCleanReload = async () => {
    if (diagnosticMode) {
      console.log('🦊 [DIAGNOSTIC] Forcing Firefox clean reload...');
    }

    await firefoxAuthHandler.forceCleanReload();
  };

  const testProductionFirefoxAuthFix = async () => {
    if (diagnosticMode) {
      console.log('🦊 [DIAGNOSTIC] Testing production Firefox auth fix...');
    }

    try {
      console.log('🦊 Starting production Firefox authentication protection test...');
      productionFirefoxAuthFix.startAuthenticationProtection();
      
      // Wait a moment to see the protection in action
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('🦊 Ending production Firefox authentication protection test...');
      productionFirefoxAuthFix.endAuthenticationProtection();
      
      console.log('✅ Production Firefox auth fix test completed');
    } catch (error) {
      console.error('❌ Production Firefox auth fix test failed:', error);
    }
  };

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Authentication Testing & Debugging
      </Typography>
      
      <Grid container spacing={3}>
        {/* Browser Information */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Browser Information
            </Typography>

            <List dense>
              <ListItem>
                <ListItemText
                  primary="Browser"
                  secondary={isFirefox() ? `Firefox ${getFirefoxVersion()}` : 'Not Firefox'}
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Firefox Auth Fallback"
                  secondary={shouldUseFirefoxAuthFallback() ? 'Enabled' : 'Disabled'}
                />
              </ListItem>
              {isFirefox() && (
                <>
                  <ListItem>
                    <ListItemText
                      primary="Firefox Debug Info"
                      secondary={
                        <pre style={{ fontSize: '0.75rem', margin: 0 }}>
                          {JSON.stringify(getFirefoxDebugInfo(), null, 2)}
                        </pre>
                      }
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Production Firefox Fix"
                      secondary={
                        <pre style={{ fontSize: '0.75rem', margin: 0 }}>
                          {JSON.stringify(productionFirefoxFix.getDebugInfo(), null, 2)}
                        </pre>
                      }
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Production Firefox Auth Fix (NEW)"
                      secondary={
                        <pre style={{ fontSize: '0.75rem', margin: 0 }}>
                          {JSON.stringify(productionFirefoxAuthFix.getDebugInfo(), null, 2)}
                        </pre>
                      }
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Early Firefox Fix (Critical)"
                      secondary={
                        <pre style={{ fontSize: '0.75rem', margin: 0 }}>
                          {JSON.stringify(getEarlyFirefoxDebugInfo(), null, 2)}
                        </pre>
                      }
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Environment Detection Check"
                      secondary={
                        <pre style={{ fontSize: '0.75rem', margin: 0 }}>
                          {JSON.stringify({
                            earlyFixProduction: window.location.protocol === 'https:' || (typeof import.meta !== 'undefined' && import.meta.env?.PROD),
                            prodFixProduction: (typeof import.meta !== 'undefined' && import.meta.env?.PROD) || window.location.protocol === 'https:',
                            protocol: window.location.protocol,
                            hostname: window.location.hostname,
                            importMetaEnv: typeof import.meta !== 'undefined' ? import.meta.env?.PROD : 'undefined',
                            consistent: ((window.location.protocol === 'https:' || (typeof import.meta !== 'undefined' && import.meta.env?.PROD)) ===
                                        ((typeof import.meta !== 'undefined' && import.meta.env?.PROD) || window.location.protocol === 'https:'))
                          }, null, 2)}
                        </pre>
                      }
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Firefox Auth Recovery System"
                      secondary={
                        <pre style={{ fontSize: '0.75rem', margin: 0 }}>
                          {JSON.stringify(firefoxAuthRecovery.getDebugInfo(), null, 2)}
                        </pre>
                      }
                    />
                  </ListItem>
                </>
              )}
            </List>
          </Paper>
        </Grid>

        {/* Authentication Status */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Authentication Status
            </Typography>
            
            <List dense>
              <ListItem>
                <ListItemText
                  primary="Loading"
                  secondary={loading ? 'Yes' : 'No'}
                />
                {loading && <CircularProgress size={20} />}
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Is Authenticated"
                  secondary={isAuthenticated ? 'Yes' : 'No'}
                />
                <Chip 
                  label={isAuthenticated ? 'Authenticated' : 'Not Authenticated'} 
                  color={isAuthenticated ? 'success' : 'default'}
                  size="small"
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Is Guest"
                  secondary={isGuest ? 'Yes' : 'No'}
                />
                <Chip 
                  label={isGuest ? 'Guest' : 'User'} 
                  color={isGuest ? 'warning' : 'primary'}
                  size="small"
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Account Type"
                  secondary={accountType || 'None'}
                />
                <Chip 
                  label={accountType || 'None'} 
                  color={accountType === 'pro' ? 'primary' : accountType === 'edu' ? 'secondary' : 'default'}
                  size="small"
                />
              </ListItem>
            </List>
          </Paper>
        </Grid>

        {/* User Information */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              User Information
            </Typography>
            
            {user ? (
              <List dense>
                <ListItem>
                  <ListItemText
                    primary="User ID"
                    secondary={user.id}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Email"
                    secondary={user.email}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Created At"
                    secondary={new Date(user.created_at || '').toLocaleString()}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Email Confirmed"
                    secondary={user.email_confirmed_at ? 'Yes' : 'No'}
                  />
                </ListItem>
              </List>
            ) : (
              <Alert severity="info">No user data available</Alert>
            )}
          </Paper>
        </Grid>

        {/* Profile Information */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">
                Profile Information
              </Typography>
              <Button
                variant="outlined"
                size="small"
                startIcon={refreshing ? <CircularProgress size={16} /> : <RefreshIcon />}
                onClick={handleRefreshProfile}
                disabled={refreshing || !user}
              >
                {refreshing ? 'Refreshing...' : 'Refresh Profile'}
              </Button>
            </Box>
            
            {userProfile ? (
              <Grid container spacing={2}>
                <Grid item xs={12} md={3} sx={{ textAlign: 'center' }}>
                  <Avatar
                    sx={{ width: 80, height: 80, mx: 'auto', mb: 1 }}
                    src={userProfile.avatar_url || undefined}
                  >
                    <PersonIcon fontSize="large" />
                  </Avatar>
                  <Typography variant="body2" color="text.secondary">
                    Avatar
                  </Typography>
                </Grid>
                <Grid item xs={12} md={9}>
                  <List dense>
                    <ListItem>
                      <ListItemText
                        primary="Username"
                        secondary={userProfile.username || 'Not set'}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Full Name"
                        secondary={userProfile.full_name || 'Not set'}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Institution"
                        secondary={userProfile.institution || 'Not set'}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Country"
                        secondary={userProfile.country || 'Not set'}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Account Type (from profile)"
                        secondary={userProfile.accounttype || 'Not set'}
                      />
                    </ListItem>
                  </List>
                </Grid>
              </Grid>
            ) : user ? (
              <Alert severity="warning">
                User is authenticated but no profile data available. 
                Try refreshing the profile.
              </Alert>
            ) : (
              <Alert severity="info">
                No profile data available (user not authenticated)
              </Alert>
            )}
          </Paper>
        </Grid>

        {/* Actions */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Actions
            </Typography>
            
            <Box display="flex" gap={2} flexWrap="wrap">
              <Button
                variant="outlined"
                onClick={() => window.location.hash = 'auth'}
                disabled={isAuthenticated}
              >
                Go to Login
              </Button>
              
              <Button
                variant="outlined"
                onClick={() => window.location.hash = 'user-profile'}
                disabled={!isAuthenticated}
              >
                Go to Profile
              </Button>
              
              <Button
                variant="outlined"
                color="error"
                onClick={handleLogout}
                disabled={!isAuthenticated}
              >
                Logout
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* Debug Information */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Debug Information
            </Typography>
            
            <Typography variant="body2" component="pre" sx={{ 
              backgroundColor: 'grey.100', 
              p: 2, 
              borderRadius: 1,
              overflow: 'auto',
              fontSize: '0.75rem'
            }}>
              {JSON.stringify({
                user: user ? {
                  id: user.id,
                  email: user.email,
                  created_at: user.created_at,
                  email_confirmed_at: user.email_confirmed_at
                } : null,
                userProfile,
                accountType,
                isAuthenticated,
                isGuest,
                loading
              }, null, 2)}
            </Typography>
          </Paper>
        </Grid>

        {/* Authentication Diagnostics */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Authentication Diagnostics
            </Typography>

            <FormControlLabel
              control={
                <Switch
                  checked={diagnosticMode}
                  onChange={(e) => setDiagnosticMode(e.target.checked)}
                />
              }
              label="Enable Diagnostic Mode"
            />

            {diagnosticMode && (
              <Box sx={{ mt: 2 }}>
                <Grid container spacing={2}>
                  {/* Diagnostic Controls */}
                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          Diagnostic Controls
                        </Typography>

                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                          <Button
                            variant="outlined"
                            onClick={testCrossTabLogout}
                            size="small"
                          >
                            Test Cross-Tab Logout
                          </Button>

                          <Button
                            variant="outlined"
                            onClick={testCrossTabLogin}
                            size="small"
                          >
                            Test Cross-Tab Login
                          </Button>

                          <Button
                            variant="outlined"
                            onClick={simulateNetworkError}
                            color="warning"
                            size="small"
                          >
                            Simulate Network Error
                          </Button>

                          <Button
                            variant="outlined"
                            onClick={clearAllAuthStorage}
                            color="error"
                            size="small"
                          >
                            Clear All Auth Storage
                          </Button>

                          <Button
                            variant="outlined"
                            onClick={clearAuthLogs}
                            size="small"
                          >
                            Clear Logs
                          </Button>

                          {isFirefox() && (
                            <>
                              <Button
                                variant="outlined"
                                onClick={testFirefoxAuthPreparation}
                                color="info"
                                size="small"
                              >
                                Test Firefox Auth Prep
                              </Button>

                              <Button
                                variant="outlined"
                                onClick={forceFirefoxCleanReload}
                                color="warning"
                                size="small"
                              >
                                Force Firefox Clean Reload
                              </Button>

                              <Button
                                variant="outlined"
                                onClick={testProductionFirefoxAuthFix}
                                color="secondary"
                                size="small"
                              >
                                Test Production Auth Fix
                              </Button>
                            </>
                          )}
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* Storage Monitor */}
                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          Storage Monitor
                        </Typography>

                        <TableContainer sx={{ maxHeight: 200 }}>
                          <Table size="small">
                            <TableHead>
                              <TableRow>
                                <TableCell>Key</TableCell>
                                <TableCell>Value</TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {Object.entries(storageData).map(([key, value]) => (
                                <TableRow key={key}>
                                  <TableCell sx={{ fontSize: '0.75rem' }}>{key}</TableCell>
                                  <TableCell sx={{ fontSize: '0.75rem' }}>{value}</TableCell>
                                </TableRow>
                              ))}
                              {Object.keys(storageData).length === 0 && (
                                <TableRow>
                                  <TableCell colSpan={2} align="center">
                                    No auth-related storage data
                                  </TableCell>
                                </TableRow>
                              )}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* Authentication Logs */}
                  <Grid item xs={12}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          Authentication Logs (Last 50)
                        </Typography>

                        <Box sx={{
                          maxHeight: 300,
                          overflow: 'auto',
                          backgroundColor: '#f5f5f5',
                          p: 1,
                          borderRadius: 1,
                          fontFamily: 'monospace',
                          fontSize: '0.75rem'
                        }}>
                          {authLogs.length > 0 ? (
                            authLogs.map((log, index) => (
                              <div key={index} style={{ marginBottom: '2px' }}>
                                {log}
                              </div>
                            ))
                          ) : (
                            <div>No authentication logs captured yet...</div>
                          )}
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AuthTestPage;
