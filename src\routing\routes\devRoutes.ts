// Development-only routes (only available in development environment)

import { lazy } from 'react';
import { EnhancedRouteConfig } from '../../types/routing';

// Lazy load development components
const DevTrainingPage = lazy(() => import('../../pages/DevTrainingPage'));
const RouteTestPage = lazy(() => import('../../pages/RouteTestPage'));
const AuthTestPage = lazy(() => import('../../pages/AuthTestPage'));
const EducationalTierTest = lazy(() => import('../../components/Testing/EducationalTierTest'));

/**
 * Check if we're in development environment
 */
const isDevelopmentEnvironment = (): boolean => {
  return process.env.NODE_ENV === 'development' || 
         (typeof window !== 'undefined' && (
           window.location.hostname === 'localhost' ||
           window.location.hostname === '127.0.0.1' ||
           window.location.port === '5173' ||
           window.location.port === '5174' ||
           window.location.port === '3000'
         ));
};

export const devRoutes: EnhancedRouteConfig[] = [
  // Development Training System
  {
    path: 'dev-training',
    component: DevTrainingPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true,
    metadata: {
      title: 'Development Training System',
      description: 'Analysis Assistant training data management (Development Only)',
      category: 'development',
      icon: 'Code',
      order: 999,
      hidden: !isDevelopmentEnvironment(), // Hide in production
      developmentOnly: true
    }
  },
  // Route Testing & Debugging
  {
    path: 'route-test',
    component: RouteTestPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true,
    metadata: {
      title: 'Route Testing & Debugging',
      description: 'Test and debug routing functionality (Development Only)',
      category: 'development',
      icon: 'Route',
      order: 998,
      hidden: !isDevelopmentEnvironment(), // Hide in production
      developmentOnly: true
    }
  },
  // Authentication Testing & Debugging
  {
    path: 'auth-test',
    component: AuthTestPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true,
    metadata: {
      title: 'Authentication Testing & Debugging',
      description: 'Test and debug authentication functionality',
      category: 'development',
      icon: 'Security',
      order: 997,
      hidden: false, // Always available for debugging authentication issues
      developmentOnly: false // Make available in production for Firefox debugging
    }
  },
  // Educational Tier Testing
  {
    path: 'edu-tier-test',
    component: EducationalTierTest,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true,
    metadata: {
      title: 'Educational Tier Testing',
      description: 'Test and verify educational tier implementation (Development Only)',
      category: 'development',
      icon: 'School',
      order: 996,
      hidden: !isDevelopmentEnvironment(), // Hide in production
      developmentOnly: true
    }
  }
];

/**
 * Get development routes (only returns routes if in development environment)
 * Exception: auth-test is always available for debugging authentication issues
 */
export const getDevRoutes = (): EnhancedRouteConfig[] => {
  if (!isDevelopmentEnvironment()) {
    // In production, only return auth-test route for debugging
    return devRoutes.filter(route => route.path === 'auth-test');
  }
  return devRoutes;
};
