/**
 * Firefox Real-time Connection Blocker
 * 
 * Aggressively blocks WebSocket and real-time connection attempts in Firefox
 * to prevent NS_ERROR_CONTENT_BLOCKED errors
 */

// Firefox detection
function isFirefoxBrowser(): boolean {
  return /Firefox/.test(navigator.userAgent);
}

// Production environment detection
function isProductionEnvironment(): boolean {
  const isHttps = window.location.protocol === 'https:';
  const isProductionDomain = window.location.hostname === 'datastatpro.com' || 
                            window.location.hostname.includes('datastatpro');
  const hasImportMetaProd = typeof import.meta !== 'undefined' && import.meta.env?.PROD;
  
  return isHttps || isProductionDomain || hasImportMetaProd;
}

/**
 * Initialize aggressive real-time connection blocking for Firefox
 */
export function initializeFirefoxRealtimeBlocking(): void {
  if (!isFirefoxBrowser() || !isProductionEnvironment()) {
    return;
  }

  console.log('🦊 Initializing aggressive Firefox real-time connection blocking...');

  // Block WebSocket connections to Supabase
  const originalWebSocket = window.WebSocket;
  window.WebSocket = class extends WebSocket {
    constructor(url: string | URL, protocols?: string | string[]) {
      const urlString = typeof url === 'string' ? url : url.toString();
      
      // Block Supabase real-time WebSocket connections
      if (urlString.includes('supabase.co') && urlString.includes('realtime')) {
        console.log('🦊 Blocking Supabase real-time WebSocket connection:', urlString);
        
        // Create a mock WebSocket that immediately closes
        super('data:,'); // Use data URL to avoid network request
        
        // Immediately close and mark as blocked
        setTimeout(() => {
          this.close(1000, 'Firefox content blocking prevention');
        }, 0);
        
        return;
      }
      
      // Allow other WebSocket connections
      super(url, protocols);
    }
  } as any;

  // Block EventSource connections (Server-Sent Events)
  const originalEventSource = window.EventSource;
  if (originalEventSource) {
    window.EventSource = class extends EventSource {
      constructor(url: string | URL, eventSourceInitDict?: EventSourceInit) {
        const urlString = typeof url === 'string' ? url : url.toString();
        
        // Block Supabase real-time EventSource connections
        if (urlString.includes('supabase.co') && urlString.includes('realtime')) {
          console.log('🦊 Blocking Supabase real-time EventSource connection:', urlString);
          
          // Create a mock EventSource that immediately closes
          super('data:,');
          
          // Immediately close
          setTimeout(() => {
            this.close();
          }, 0);
          
          return;
        }
        
        // Allow other EventSource connections
        super(url, eventSourceInitDict);
      }
    } as any;
  }

  // Override fetch to block real-time related requests
  const originalFetch = window.fetch;
  window.fetch = async function(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
    const url = typeof input === 'string' ? input : 
                input instanceof URL ? input.toString() : 
                input.url;
    
    // Block Supabase real-time related fetch requests
    if (url.includes('supabase.co') && (url.includes('realtime') || url.includes('websocket'))) {
      console.log('🦊 Blocking Supabase real-time fetch request:', url);
      
      // Return a mock response
      return new Response(JSON.stringify({ 
        error: 'Firefox real-time blocking active',
        blocked: true 
      }), {
        status: 200,
        statusText: 'OK',
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    
    // Allow other fetch requests
    return originalFetch.call(this, input, init);
  };

  console.log('🦊 Firefox real-time connection blocking initialized');
  
  // Set flag to indicate blocking is active
  localStorage.setItem('firefox-realtime-blocked', 'true');
}

/**
 * Disable Firefox real-time blocking (for testing)
 */
export function disableFirefoxRealtimeBlocking(): void {
  console.log('🦊 Disabling Firefox real-time blocking...');
  
  // Note: This is a simplified disable - in practice, you'd need to restore original functions
  // For now, just clear the flag
  localStorage.removeItem('firefox-realtime-blocked');
  
  console.log('🦊 Firefox real-time blocking disabled (page refresh may be required)');
}

/**
 * Check if Firefox real-time blocking is active
 */
export function isFirefoxRealtimeBlockingActive(): boolean {
  return isFirefoxBrowser() && 
         isProductionEnvironment() && 
         localStorage.getItem('firefox-realtime-blocked') === 'true';
}

/**
 * Get Firefox real-time blocking status
 */
export function getFirefoxRealtimeBlockingStatus(): {
  isFirefox: boolean;
  isProduction: boolean;
  blockingActive: boolean;
  shouldBlock: boolean;
} {
  const isFirefox = isFirefoxBrowser();
  const isProduction = isProductionEnvironment();
  const blockingActive = localStorage.getItem('firefox-realtime-blocked') === 'true';
  const shouldBlock = isFirefox && isProduction;
  
  return {
    isFirefox,
    isProduction,
    blockingActive,
    shouldBlock
  };
}
