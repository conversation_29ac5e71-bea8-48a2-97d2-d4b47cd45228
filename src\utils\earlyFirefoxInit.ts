/**
 * Early Firefox initialization that runs before service worker registration
 * This must be imported and executed before any PWA service worker registration
 */

// Simple Firefox detection (without importing other modules to avoid circular dependencies)
function isFirefoxBrowser(): boolean {
  return /Firefox/.test(navigator.userAgent);
}

// Simple production detection - FIXED: Use consistent detection method
function isProductionEnvironment(): boolean {
  // Use the same logic as productionFirefoxFix.ts for consistency
  return (typeof import.meta !== 'undefined' && import.meta.env?.PROD) || window.location.protocol === 'https:';
}

/**
 * Early Firefox production fix initialization
 * This runs immediately when the module is imported
 */
export function initializeEarlyFirefoxFix(): void {
  // Only run for Firefox in production
  if (!isFirefoxBrowser() || !isProductionEnvironment()) {
    console.log('🦊 Early Firefox fix: Skipping initialization', {
      isFirefox: isFirefoxBrowser(),
      isProduction: isProductionEnvironment(),
      protocol: window.location.protocol,
      hostname: window.location.hostname,
      importMetaEnv: typeof import.meta !== 'undefined' ? import.meta.env?.PROD : 'undefined'
    });
    return;
  }

  console.log('🦊 Early Firefox production fix initializing...', {
    isFirefox: isFirefoxBrowser(),
    isProduction: isProductionEnvironment(),
    protocol: window.location.protocol,
    hostname: window.location.hostname,
    importMetaEnv: typeof import.meta !== 'undefined' ? import.meta.env?.PROD : 'undefined'
  });

  // Store original service worker register function before it can be called
  if ('serviceWorker' in navigator && navigator.serviceWorker.register) {
    const originalRegister = navigator.serviceWorker.register.bind(navigator.serviceWorker);

    // Override service worker registration immediately
    navigator.serviceWorker.register = async (scriptURL: string | URL, options?: RegistrationOptions) => {
      console.log('🦊 Early Firefox fix: Service worker registration intercepted');
      console.log('🦊 Script URL:', scriptURL);
      console.log('🦊 Options:', options);

      // Check if authentication is in progress
      const isAuthInProgress = localStorage.getItem('datastatpro-auth-in-progress') === 'true';

      console.log('🦊 Authentication status check:', {
        isAuthInProgress,
        authFlag: localStorage.getItem('datastatpro-auth-in-progress'),
        timestamp: new Date().toISOString()
      });

      if (isAuthInProgress) {
        console.log('🦊 Blocking service worker registration during authentication');

        // Return a mock registration that won't interfere
        return createMockServiceWorkerRegistration();
      }

      // For non-auth contexts, allow registration but with Firefox-safe options
      try {
        const modifiedOptions = {
          ...options,
          scope: options?.scope || '/',
          // Add Firefox-specific options for better compatibility
          updateViaCache: 'none' as ServiceWorkerUpdateViaCache
        };

        console.log('🦊 Allowing service worker registration with modified options:', modifiedOptions);
        const registration = await originalRegister(scriptURL, modifiedOptions);
        console.log('🦊 Service worker registration successful:', registration);
        return registration;
      } catch (error) {
        console.warn('🦊 Service worker registration failed, returning mock:', error);
        console.warn('🦊 Error details:', {
          message: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
          scriptURL: String(scriptURL),
          options: modifiedOptions
        });
        return createMockServiceWorkerRegistration();
      }
    };

    console.log('✅ Early Firefox fix: Service worker registration intercepted successfully');
  }

  // Set up authentication state monitoring
  setupAuthenticationStateMonitoring();

  console.log('✅ Early Firefox production fix initialized');
}

/**
 * Creates a mock service worker registration that doesn't interfere with authentication
 */
function createMockServiceWorkerRegistration(): Promise<ServiceWorkerRegistration> {
  return Promise.resolve({
    scope: '/',
    installing: null,
    waiting: null,
    active: null,
    navigationPreload: {
      enable: () => Promise.resolve(),
      disable: () => Promise.resolve(),
      setHeaderValue: () => Promise.resolve(),
      getState: () => Promise.resolve({ enabled: false, headerValue: '' })
    },
    pushManager: {
      supportedContentEncodings: [],
      subscribe: () => Promise.reject(new Error('Push not supported in Firefox production mode')),
      getSubscription: () => Promise.resolve(null),
      permissionState: () => Promise.resolve('denied' as PushPermissionState)
    },
    sync: {
      register: () => Promise.resolve(),
      getTags: () => Promise.resolve([])
    },
    addEventListener: () => {},
    removeEventListener: () => {},
    dispatchEvent: () => false,
    unregister: () => Promise.resolve(true),
    update: () => Promise.resolve()
  } as ServiceWorkerRegistration);
}

/**
 * Sets up monitoring for authentication state changes
 */
function setupAuthenticationStateMonitoring(): void {
  // Listen for authentication state changes across tabs
  window.addEventListener('storage', (event) => {
    if (event.key === 'firefox-auth-success' && event.newValue === 'true') {
      console.log('🦊 Early Firefox fix: Authentication success detected in another tab');
      
      // Clear the flag
      localStorage.removeItem('firefox-auth-success');
      
      // Reload the page to ensure clean state
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
  });

  // Monitor for authentication completion
  const checkAuthCompletion = () => {
    const authInProgress = localStorage.getItem('datastatpro-auth-in-progress');
    const authSuccess = localStorage.getItem('firefox-auth-success');
    
    if (authSuccess === 'true') {
      console.log('🦊 Early Firefox fix: Authentication completed successfully');
      localStorage.removeItem('firefox-auth-success');
      
      // Clear any authentication flags
      localStorage.removeItem('datastatpro-auth-in-progress');
      
      // Signal completion to other components
      window.dispatchEvent(new CustomEvent('firefox-auth-completed'));
    }
  };

  // Check periodically for auth completion
  setInterval(checkAuthCompletion, 1000);
}

/**
 * Signals authentication start from the early fix
 */
export function signalAuthenticationStart(): void {
  if (!isFirefoxBrowser() || !isProductionEnvironment()) {
    console.log('🦊 Early Firefox fix: Skipping auth start signal', {
      isFirefox: isFirefoxBrowser(),
      isProduction: isProductionEnvironment()
    });
    return;
  }

  console.log('🦊 Early Firefox fix: Authentication starting');
  localStorage.setItem('datastatpro-auth-in-progress', 'true');
  console.log('🦊 Auth flag set:', localStorage.getItem('datastatpro-auth-in-progress'));
}

/**
 * Signals authentication success from the early fix
 */
export function signalAuthenticationSuccess(): void {
  if (!isFirefoxBrowser() || !isProductionEnvironment()) {
    console.log('🦊 Early Firefox fix: Skipping auth success signal', {
      isFirefox: isFirefoxBrowser(),
      isProduction: isProductionEnvironment()
    });
    return;
  }

  console.log('🦊 Early Firefox fix: Authentication successful');
  localStorage.setItem('firefox-auth-success', 'true');
  localStorage.removeItem('datastatpro-auth-in-progress');
  console.log('🦊 Auth success flag set, auth in progress flag cleared');

  // Remove the signal after a brief delay
  setTimeout(() => {
    localStorage.removeItem('firefox-auth-success');
    console.log('🦊 Auth success flag cleared after delay');
  }, 2000);
}

/**
 * Gets debug information about the early Firefox fix
 */
export function getEarlyFirefoxDebugInfo(): object {
  return {
    isFirefox: isFirefoxBrowser(),
    isProduction: isProductionEnvironment(),
    hasServiceWorkerOverride: 'serviceWorker' in navigator && 
      navigator.serviceWorker.register.toString().includes('Early Firefox fix'),
    authFlags: {
      'datastatpro-auth-in-progress': localStorage.getItem('datastatpro-auth-in-progress'),
      'firefox-auth-success': localStorage.getItem('firefox-auth-success')
    },
    timestamp: new Date().toISOString()
  };
}

// Auto-initialize when this module is imported
initializeEarlyFirefoxFix();
