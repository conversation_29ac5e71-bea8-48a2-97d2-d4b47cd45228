/**
 * Production-specific Firefox authentication fixes
 * Addresses service worker and PWA issues in production environment
 */

import { isFirefox } from './compatibilityChecker';

export class ProductionFirefoxFix {
  private static instance: ProductionFirefoxFix;
  private isProduction: boolean;
  private originalRegister: typeof navigator.serviceWorker.register | null = null;

  constructor() {
    // Multiple checks for production environment - consistent with earlyFirefoxInit
    const isHttps = window.location.protocol === 'https:';
    const isProductionDomain = window.location.hostname === 'datastatpro.com' ||
                              window.location.hostname.includes('datastatpro');
    const hasImportMetaProd = typeof import.meta !== 'undefined' && import.meta.env?.PROD;

    // Consider it production if any of these conditions are true
    this.isProduction = isHttps || isProductionDomain || hasImportMetaProd;
  }

  public static getInstance(): ProductionFirefoxFix {
    if (!ProductionFirefoxFix.instance) {
      ProductionFirefoxFix.instance = new ProductionFirefoxFix();
    }
    return ProductionFirefoxFix.instance;
  }

  /**
   * Initializes production Firefox fixes
   */
  public initialize(): void {
    if (!isFirefox() || !this.isProduction) {
      return;
    }

    console.log('🦊 Initializing production Firefox fixes...');

    // Intercept service worker registration in production
    this.interceptServiceWorkerRegistration();

    // Set up authentication-specific fixes
    this.setupAuthenticationFixes();

    console.log('✅ Production Firefox fixes initialized');
  }

  /**
   * Intercepts and prevents service worker registration for Firefox in production
   */
  private interceptServiceWorkerRegistration(): void {
    if (!('serviceWorker' in navigator)) return;

    // Store original register function
    this.originalRegister = navigator.serviceWorker.register.bind(navigator.serviceWorker);

    // Override service worker registration
    navigator.serviceWorker.register = async (scriptURL: string | URL, options?: RegistrationOptions) => {
      console.log('🦊 Service worker registration intercepted for Firefox in production');
      console.log('🦊 Script URL:', scriptURL);
      console.log('🦊 Options:', options);

      // Check if this is an authentication-related registration attempt
      const isAuthContext = this.isInAuthenticationContext();

      if (isAuthContext) {
        console.log('🦊 Blocking service worker registration during authentication');
        
        // Return a mock registration that won't interfere
        return this.createMockRegistration();
      }

      // For non-auth contexts, allow registration but with modified options
      try {
        const modifiedOptions = {
          ...options,
          scope: options?.scope || '/',
          // Add Firefox-specific options
          updateViaCache: 'none' as ServiceWorkerUpdateViaCache
        };

        console.log('🦊 Allowing modified service worker registration');
        return await this.originalRegister!(scriptURL, modifiedOptions);
      } catch (error) {
        console.warn('🦊 Service worker registration failed, returning mock:', error);
        return this.createMockRegistration();
      }
    };
  }

  /**
   * Checks if we're currently in an authentication context
   */
  private isInAuthenticationContext(): boolean {
    // Check for authentication-related flags
    const authFlags = [
      'firefox-sw-disabled-for-auth',
      'firefox-sw-unregistered-for-auth',
      'datastatpro-auth-in-progress'
    ];

    return authFlags.some(flag => localStorage.getItem(flag) === 'true');
  }

  /**
   * Creates a mock service worker registration
   */
  private createMockRegistration(): Promise<ServiceWorkerRegistration> {
    return Promise.resolve({
      scope: '/',
      installing: null,
      waiting: null,
      active: null,
      navigationPreload: {
        enable: () => Promise.resolve(),
        disable: () => Promise.resolve(),
        setHeaderValue: () => Promise.resolve(),
        getState: () => Promise.resolve({ enabled: false, headerValue: '' })
      },
      pushManager: {
        supportedContentEncodings: [],
        subscribe: () => Promise.reject(new Error('Push not supported in Firefox production mode')),
        getSubscription: () => Promise.resolve(null),
        permissionState: () => Promise.resolve('denied' as PushPermissionState)
      },
      sync: {
        register: () => Promise.resolve(),
        getTags: () => Promise.resolve([])
      },
      addEventListener: () => {},
      removeEventListener: () => {},
      dispatchEvent: () => false,
      unregister: () => Promise.resolve(true),
      update: () => Promise.resolve()
    } as ServiceWorkerRegistration);
  }

  /**
   * Sets up authentication-specific fixes
   */
  private setupAuthenticationFixes(): void {
    // Override fetch for authentication requests if needed
    this.setupFetchInterception();

    // Set up storage event listeners for cross-tab sync
    this.setupStorageEventHandling();
  }

  /**
   * Sets up fetch interception for authentication requests
   */
  private setupFetchInterception(): void {
    // Store original fetch
    const originalFetch = window.fetch;

    window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      const url = typeof input === 'string' ? input : input instanceof URL ? input.href : input.url;

      // Check if this is a Supabase auth request
      if (url.includes('supabase.co') && (url.includes('auth') || url.includes('token'))) {
        console.log('🦊 Intercepting Supabase auth request for Firefox production');

        // Add Firefox-specific headers
        const modifiedInit = {
          ...init,
          headers: {
            ...init?.headers,
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache'
          }
        };

        return originalFetch(input, modifiedInit);
      }

      return originalFetch(input, init);
    };
  }

  /**
   * Sets up storage event handling for cross-tab synchronization
   */
  private setupStorageEventHandling(): void {
    window.addEventListener('storage', (event) => {
      if (event.key === 'firefox-auth-success' && event.newValue === 'true') {
        console.log('🦊 Firefox authentication success detected in another tab');
        
        // Clear the flag
        localStorage.removeItem('firefox-auth-success');
        
        // Reload the page to ensure clean state
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
    });
  }

  /**
   * Signals successful authentication to other tabs
   */
  public signalAuthenticationSuccess(): void {
    if (!isFirefox() || !this.isProduction) return;

    console.log('🦊 Signaling authentication success to other tabs');
    localStorage.setItem('firefox-auth-success', 'true');

    // Remove the signal after a brief delay
    setTimeout(() => {
      localStorage.removeItem('firefox-auth-success');
    }, 2000);
  }

  /**
   * Cleans up production Firefox fixes
   */
  public cleanup(): void {
    if (!isFirefox() || !this.isProduction) return;

    console.log('🦊 Cleaning up production Firefox fixes...');

    // Restore original service worker registration if we intercepted it
    if (this.originalRegister && 'serviceWorker' in navigator) {
      navigator.serviceWorker.register = this.originalRegister;
    }

    // Clear any authentication flags
    const authFlags = [
      'firefox-sw-disabled-for-auth',
      'firefox-sw-unregistered-for-auth',
      'datastatpro-auth-in-progress',
      'firefox-auth-success'
    ];

    authFlags.forEach(flag => localStorage.removeItem(flag));

    console.log('✅ Production Firefox fixes cleaned up');
  }

  /**
   * Checks if production fixes are active
   */
  public isActive(): boolean {
    return isFirefox() && this.isProduction;
  }

  /**
   * Gets debug information about the current state
   */
  public getDebugInfo(): object {
    return {
      isFirefox: isFirefox(),
      isProduction: this.isProduction,
      isActive: this.isActive(),
      hasOriginalRegister: this.originalRegister !== null,
      authFlags: {
        'firefox-sw-disabled-for-auth': localStorage.getItem('firefox-sw-disabled-for-auth'),
        'firefox-sw-unregistered-for-auth': localStorage.getItem('firefox-sw-unregistered-for-auth'),
        'datastatpro-auth-in-progress': localStorage.getItem('datastatpro-auth-in-progress'),
        'firefox-auth-success': localStorage.getItem('firefox-auth-success')
      },
      timestamp: new Date().toISOString()
    };
  }
}

// Export singleton instance
export const productionFirefoxFix = ProductionFirefoxFix.getInstance();
