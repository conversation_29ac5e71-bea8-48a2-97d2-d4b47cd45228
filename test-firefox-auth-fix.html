<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firefox Authentication Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.warning {
            background-color: #ffc107;
            color: #212529;
        }
        button.warning:hover {
            background-color: #e0a800;
        }
    </style>
</head>
<body>
    <h1>Firefox Authentication Fix Test</h1>
    <p>This page tests the Firefox authentication fix for DataStatPro.</p>

    <div class="test-section">
        <h2>Browser Detection</h2>
        <div id="browser-info"></div>
    </div>

    <div class="test-section">
        <h2>Environment Detection Test</h2>
        <div id="environment-test"></div>
        <button onclick="testEnvironmentDetection()">Run Environment Test</button>
    </div>

    <div class="test-section">
        <h2>Service Worker Test</h2>
        <div id="service-worker-test"></div>
        <button onclick="testServiceWorker()">Test Service Worker</button>
    </div>

    <div class="test-section">
        <h2>Authentication Flags Test</h2>
        <div id="auth-flags-test"></div>
        <button onclick="testAuthFlags()">Test Auth Flags</button>
        <button onclick="clearAuthFlags()" class="warning">Clear Auth Flags</button>
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="test-results"></div>
        <button onclick="runAllTests()">Run All Tests</button>
    </div>

    <script>
        // Simple Firefox detection
        function isFirefoxBrowser() {
            return /Firefox/.test(navigator.userAgent);
        }

        // Environment detection methods
        function earlyFixProductionDetection() {
            return window.location.protocol === 'https:' || (typeof import.meta !== 'undefined' && import.meta.env?.PROD);
        }

        function prodFixProductionDetection() {
            return (typeof import.meta !== 'undefined' && import.meta.env?.PROD) || window.location.protocol === 'https:';
        }

        // Initialize browser info
        function initBrowserInfo() {
            const browserInfo = document.getElementById('browser-info');
            const isFirefox = isFirefoxBrowser();
            const firefoxVersion = isFirefox ? navigator.userAgent.match(/Firefox\/(\d+)/)?.[1] : null;
            
            browserInfo.innerHTML = `
                <div class="status ${isFirefox ? 'pass' : 'warning'}">
                    <strong>Browser:</strong> ${isFirefox ? `Firefox ${firefoxVersion}` : 'Not Firefox'}
                </div>
                <div class="status ${isFirefox ? 'pass' : 'fail'}">
                    <strong>Firefox Authentication Fix:</strong> ${isFirefox ? 'Applicable' : 'Not Applicable'}
                </div>
            `;
        }

        function testEnvironmentDetection() {
            const testDiv = document.getElementById('environment-test');
            
            const earlyFix = earlyFixProductionDetection();
            const prodFix = prodFixProductionDetection();
            const consistent = earlyFix === prodFix;
            
            const envInfo = {
                protocol: window.location.protocol,
                hostname: window.location.hostname,
                importMetaEnv: typeof import.meta !== 'undefined' ? import.meta.env?.PROD : 'undefined',
                earlyFixProduction: earlyFix,
                prodFixProduction: prodFix,
                consistent: consistent
            };
            
            testDiv.innerHTML = `
                <div class="status ${consistent ? 'pass' : 'fail'}">
                    <strong>Environment Detection Consistency:</strong> ${consistent ? 'PASS' : 'FAIL'}
                </div>
                <pre>${JSON.stringify(envInfo, null, 2)}</pre>
                ${!consistent ? '<div class="status fail"><strong>Issue:</strong> Inconsistent environment detection may cause authentication failures!</div>' : ''}
            `;
        }

        function testServiceWorker() {
            const testDiv = document.getElementById('service-worker-test');
            
            if (!('serviceWorker' in navigator)) {
                testDiv.innerHTML = '<div class="status fail">Service Worker not supported</div>';
                return;
            }
            
            const hasOverride = navigator.serviceWorker.register.toString().includes('Early Firefox fix') ||
                               navigator.serviceWorker.register.toString().includes('Service worker registration intercepted');
            
            testDiv.innerHTML = `
                <div class="status ${hasOverride ? 'pass' : 'warning'}">
                    <strong>Service Worker Override:</strong> ${hasOverride ? 'Active' : 'Not Active'}
                </div>
                <pre>Service Worker Register Function:
${navigator.serviceWorker.register.toString().substring(0, 200)}...</pre>
            `;
        }

        function testAuthFlags() {
            const testDiv = document.getElementById('auth-flags-test');
            
            const flags = {
                'datastatpro-auth-in-progress': localStorage.getItem('datastatpro-auth-in-progress'),
                'firefox-auth-success': localStorage.getItem('firefox-auth-success'),
                'firefox-sw-disabled-for-auth': localStorage.getItem('firefox-sw-disabled-for-auth'),
                'firefox-sw-unregistered-for-auth': localStorage.getItem('firefox-sw-unregistered-for-auth')
            };
            
            const hasActiveFlags = Object.values(flags).some(value => value === 'true');
            
            testDiv.innerHTML = `
                <div class="status ${hasActiveFlags ? 'warning' : 'pass'}">
                    <strong>Authentication Flags:</strong> ${hasActiveFlags ? 'Some flags are active' : 'No active flags'}
                </div>
                <pre>${JSON.stringify(flags, null, 2)}</pre>
            `;
        }

        function clearAuthFlags() {
            const flags = [
                'datastatpro-auth-in-progress',
                'firefox-auth-success',
                'firefox-sw-disabled-for-auth',
                'firefox-sw-unregistered-for-auth'
            ];
            
            flags.forEach(flag => localStorage.removeItem(flag));
            alert('Authentication flags cleared');
            testAuthFlags();
        }

        function runAllTests() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<p>Running all tests...</p>';

            setTimeout(() => {
                testEnvironmentDetection();
                testServiceWorker();
                testAuthFlags();

                const isFirefox = isFirefoxBrowser();
                const envConsistent = earlyFixProductionDetection() === prodFixProductionDetection();
                const hasOverride = navigator.serviceWorker.register.toString().includes('Early Firefox fix') ||
                                   navigator.serviceWorker.register.toString().includes('Service worker registration intercepted');

                // Additional checks for the new fixes
                const isProductionDomain = window.location.hostname === 'datastatpro.com' ||
                                          window.location.hostname.includes('datastatpro');
                const isHttps = window.location.protocol === 'https:';

                let overallStatus = 'pass';
                let issues = [];
                let recommendations = [];

                if (isFirefox && !envConsistent) {
                    overallStatus = 'fail';
                    issues.push('Environment detection inconsistency');
                }

                if (isFirefox && !hasOverride && (isHttps || isProductionDomain)) {
                    overallStatus = 'warning';
                    issues.push('Service worker override not detected in production environment');
                }

                if (isFirefox && isProductionDomain) {
                    recommendations.push('Firefox production fixes should be active');
                    recommendations.push('Check browser console for Firefox authentication logs');
                    recommendations.push('Use /app#auth-test for detailed diagnostics');
                }

                resultsDiv.innerHTML = `
                    <div class="status ${overallStatus}">
                        <strong>Overall Test Result:</strong> ${overallStatus.toUpperCase()}
                    </div>
                    <div class="status pass">
                        <strong>Environment Info:</strong>
                        <ul>
                            <li>Production Domain: ${isProductionDomain ? 'Yes' : 'No'}</li>
                            <li>HTTPS: ${isHttps ? 'Yes' : 'No'}</li>
                            <li>Firefox: ${isFirefox ? 'Yes' : 'No'}</li>
                        </ul>
                    </div>
                    ${issues.length > 0 ? `<div class="status fail"><strong>Issues Found:</strong><ul>${issues.map(issue => `<li>${issue}</li>`).join('')}</ul></div>` : ''}
                    ${recommendations.length > 0 ? `<div class="status warning"><strong>Recommendations:</strong><ul>${recommendations.map(rec => `<li>${rec}</li>`).join('')}</ul></div>` : ''}
                    <p><strong>Status:</strong> ${
                        overallStatus === 'pass' ? 'Firefox authentication should work correctly with the new fixes.' :
                        overallStatus === 'warning' ? 'Firefox authentication should work with the new fixes, but monitoring is recommended.' :
                        'Firefox authentication issues may persist. Check the application logs and use the auth-test page for detailed diagnostics.'
                    }</p>
                `;
            }, 100);
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initBrowserInfo();
            runAllTests();
        });
    </script>
</body>
</html>
