<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hostinger Environment Diagnostic</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagnostic-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-good { color: #4CAF50; font-weight: bold; }
        .status-warning { color: #FF9800; font-weight: bold; }
        .status-error { color: #F44336; font-weight: bold; }
        .test-button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #1565c0;
        }
        .result-box {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        h1 { color: #1976d2; }
        h2 { color: #333; border-bottom: 2px solid #1976d2; padding-bottom: 5px; }
    </style>
</head>
<body>
    <h1>🔍 Hostinger Environment Diagnostic Tool</h1>
    <p>This tool helps diagnose differences between your local WAMP environment and Hostinger hosting.</p>

    <div class="diagnostic-section">
        <h2>🔒 HTTPS & Security Context</h2>
        <div id="https-status"></div>
        <div id="secure-context-status"></div>
        <div id="service-worker-support"></div>
    </div>

    <div class="diagnostic-section">
        <h2>🌐 Network & CORS</h2>
        <button class="test-button" onclick="testSupabaseConnectivity()">Test Supabase Connectivity</button>
        <button class="test-button" onclick="testCORSHeaders()">Test CORS Headers</button>
        <div id="network-results"></div>
    </div>

    <div class="diagnostic-section">
        <h2>📱 PWA Features</h2>
        <button class="test-button" onclick="testPWAFeatures()">Test PWA Support</button>
        <button class="test-button" onclick="testServiceWorker()">Test Service Worker</button>
        <div id="pwa-results"></div>
    </div>

    <div class="diagnostic-section">
        <h2>🦊 Firefox-Specific Tests</h2>
        <button class="test-button" onclick="testFirefoxFeatures()">Test Firefox Compatibility</button>
        <button class="test-button" onclick="testWebSocketSupport()">Test WebSocket Support</button>
        <div id="firefox-results"></div>
    </div>

    <div class="diagnostic-section">
        <h2>💾 Storage & Cache</h2>
        <button class="test-button" onclick="testStorageAPIs()">Test Storage APIs</button>
        <button class="test-button" onclick="clearAllCaches()">Clear All Caches</button>
        <div id="storage-results"></div>
    </div>

    <script>
        // Initialize diagnostic on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkHTTPSStatus();
            checkSecureContext();
            checkServiceWorkerSupport();
        });

        function checkHTTPSStatus() {
            const isHTTPS = location.protocol === 'https:';
            const statusEl = document.getElementById('https-status');
            statusEl.innerHTML = `
                <strong>Protocol:</strong> ${location.protocol} 
                <span class="${isHTTPS ? 'status-good' : 'status-error'}">
                    ${isHTTPS ? '✅ HTTPS (Required for PWA)' : '❌ HTTP (PWA features disabled)'}
                </span>
            `;
        }

        function checkSecureContext() {
            const isSecure = window.isSecureContext;
            const statusEl = document.getElementById('secure-context-status');
            statusEl.innerHTML = `
                <strong>Secure Context:</strong> 
                <span class="${isSecure ? 'status-good' : 'status-error'}">
                    ${isSecure ? '✅ Yes' : '❌ No (Required for many APIs)'}
                </span>
            `;
        }

        function checkServiceWorkerSupport() {
            const supported = 'serviceWorker' in navigator;
            const statusEl = document.getElementById('service-worker-support');
            statusEl.innerHTML = `
                <strong>Service Worker Support:</strong> 
                <span class="${supported ? 'status-good' : 'status-error'}">
                    ${supported ? '✅ Supported' : '❌ Not Supported'}
                </span>
            `;
        }

        async function testSupabaseConnectivity() {
            const resultsEl = document.getElementById('network-results');
            resultsEl.innerHTML = '<div class="result-box">Testing Supabase connectivity...</div>';
            
            try {
                // Test basic connectivity to Supabase
                const supabaseUrl = 'https://ghzibvkqmdlpyaidfbah.supabase.co';
                const response = await fetch(`${supabaseUrl}/rest/v1/`, {
                    method: 'HEAD',
                    headers: {
                        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdoemlidmtxbWRscHlhaWRmYmFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NzI4NzQsImV4cCI6MjA1MDU0ODg3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'
                    }
                });
                
                resultsEl.innerHTML = `
                    <div class="result-box">
                        <span class="status-good">✅ Supabase Connectivity Test</span>
                        Status: ${response.status}
                        Headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}
                    </div>
                `;
            } catch (error) {
                resultsEl.innerHTML = `
                    <div class="result-box">
                        <span class="status-error">❌ Supabase Connectivity Failed</span>
                        Error: ${error.message}
                        Stack: ${error.stack}
                    </div>
                `;
            }
        }

        async function testCORSHeaders() {
            const resultsEl = document.getElementById('network-results');
            const currentContent = resultsEl.innerHTML;
            
            try {
                const response = await fetch(location.href, { method: 'HEAD' });
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
                    'Content-Security-Policy': response.headers.get('Content-Security-Policy'),
                    'X-Frame-Options': response.headers.get('X-Frame-Options'),
                    'X-Content-Type-Options': response.headers.get('X-Content-Type-Options')
                };
                
                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="status-good">✅ CORS & Security Headers</span>
                        ${JSON.stringify(corsHeaders, null, 2)}
                    </div>
                `;
            } catch (error) {
                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="status-error">❌ CORS Test Failed</span>
                        Error: ${error.message}
                    </div>
                `;
            }
        }

        async function testPWAFeatures() {
            const resultsEl = document.getElementById('pwa-results');
            const tests = {
                'Manifest Support': 'manifest' in window,
                'Cache API': 'caches' in window,
                'IndexedDB': 'indexedDB' in window,
                'Local Storage': 'localStorage' in window,
                'Session Storage': 'sessionStorage' in window,
                'Notification API': 'Notification' in window,
                'Push API': 'PushManager' in window
            };
            
            let results = '<div class="result-box"><strong>PWA Feature Support:</strong>\n';
            for (const [feature, supported] of Object.entries(tests)) {
                const status = supported ? '✅' : '❌';
                results += `${status} ${feature}: ${supported}\n`;
            }
            results += '</div>';
            
            resultsEl.innerHTML = results;
        }

        async function testServiceWorker() {
            const resultsEl = document.getElementById('pwa-results');
            const currentContent = resultsEl.innerHTML;
            
            if ('serviceWorker' in navigator) {
                try {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    resultsEl.innerHTML = currentContent + `
                        <div class="result-box">
                            <span class="status-good">✅ Service Worker Status</span>
                            Active Registrations: ${registrations.length}
                            ${registrations.map(reg => `
                                Scope: ${reg.scope}
                                State: ${reg.active?.state || 'inactive'}
                            `).join('\n')}
                        </div>
                    `;
                } catch (error) {
                    resultsEl.innerHTML = currentContent + `
                        <div class="result-box">
                            <span class="status-error">❌ Service Worker Error</span>
                            Error: ${error.message}
                        </div>
                    `;
                }
            } else {
                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="status-error">❌ Service Worker Not Supported</span>
                    </div>
                `;
            }
        }

        function testFirefoxFeatures() {
            const resultsEl = document.getElementById('firefox-results');
            const isFirefox = navigator.userAgent.includes('Firefox');
            const firefoxVersion = isFirefox ? navigator.userAgent.match(/Firefox\/(\d+)/)?.[1] : 'N/A';
            
            resultsEl.innerHTML = `
                <div class="result-box">
                    <strong>Firefox Detection:</strong>
                    Browser: ${navigator.userAgent}
                    Is Firefox: ${isFirefox ? '✅ Yes' : '❌ No'}
                    Firefox Version: ${firefoxVersion}
                    
                    <strong>Firefox-Specific Features:</strong>
                    WebSocket: ${'WebSocket' in window ? '✅' : '❌'}
                    EventSource: ${'EventSource' in window ? '✅' : '❌'}
                    Fetch API: ${'fetch' in window ? '✅' : '❌'}
                </div>
            `;
        }

        function testWebSocketSupport() {
            const resultsEl = document.getElementById('firefox-results');
            const currentContent = resultsEl.innerHTML;
            
            try {
                // Test WebSocket creation (don't actually connect)
                const ws = new WebSocket('wss://echo.websocket.org');
                ws.close();
                
                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="status-good">✅ WebSocket Support</span>
                        WebSocket constructor available and functional
                    </div>
                `;
            } catch (error) {
                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="status-error">❌ WebSocket Error</span>
                        Error: ${error.message}
                    </div>
                `;
            }
        }

        function testStorageAPIs() {
            const resultsEl = document.getElementById('storage-results');
            
            try {
                // Test localStorage
                localStorage.setItem('diagnostic-test', 'test-value');
                const localStorageWorks = localStorage.getItem('diagnostic-test') === 'test-value';
                localStorage.removeItem('diagnostic-test');
                
                // Test sessionStorage
                sessionStorage.setItem('diagnostic-test', 'test-value');
                const sessionStorageWorks = sessionStorage.getItem('diagnostic-test') === 'test-value';
                sessionStorage.removeItem('diagnostic-test');
                
                resultsEl.innerHTML = `
                    <div class="result-box">
                        <strong>Storage API Tests:</strong>
                        Local Storage: ${localStorageWorks ? '✅ Working' : '❌ Failed'}
                        Session Storage: ${sessionStorageWorks ? '✅ Working' : '❌ Failed'}
                        IndexedDB: ${'indexedDB' in window ? '✅ Available' : '❌ Not Available'}
                        Cache API: ${'caches' in window ? '✅ Available' : '❌ Not Available'}
                    </div>
                `;
            } catch (error) {
                resultsEl.innerHTML = `
                    <div class="result-box">
                        <span class="status-error">❌ Storage Test Failed</span>
                        Error: ${error.message}
                    </div>
                `;
            }
        }

        async function clearAllCaches() {
            const resultsEl = document.getElementById('storage-results');
            const currentContent = resultsEl.innerHTML;
            
            try {
                // Clear localStorage
                localStorage.clear();
                
                // Clear sessionStorage
                sessionStorage.clear();
                
                // Clear caches if available
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    await Promise.all(cacheNames.map(name => caches.delete(name)));
                }
                
                // Unregister service workers
                if ('serviceWorker' in navigator) {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    await Promise.all(registrations.map(reg => reg.unregister()));
                }
                
                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="status-good">✅ All Caches Cleared</span>
                        - Local Storage cleared
                        - Session Storage cleared
                        - Service Worker caches cleared
                        - Service Workers unregistered
                        
                        Please refresh the page to test with clean state.
                    </div>
                `;
            } catch (error) {
                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="status-error">❌ Cache Clear Failed</span>
                        Error: ${error.message}
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
