/**
 * Firefox Content Blocking Detector
 * 
 * Detects when Firefox is blocking Supabase connections and enables fallback mode
 */

// Firefox detection
function isFirefoxBrowser(): boolean {
  return /Firefox/.test(navigator.userAgent);
}

// Production environment detection
function isProductionEnvironment(): boolean {
  const isHttps = window.location.protocol === 'https:';
  const isProductionDomain = window.location.hostname === 'datastatpro.com' || 
                            window.location.hostname.includes('datastatpro');
  const hasImportMetaProd = typeof import.meta !== 'undefined' && import.meta.env?.PROD;
  
  return isHttps || isProductionDomain || hasImportMetaProd;
}

/**
 * Test if Supabase connections are being blocked by Firefox
 */
export async function testSupabaseConnectivity(): Promise<{
  blocked: boolean;
  error?: string;
  details?: any;
}> {
  if (!isFirefoxBrowser() || !isProductionEnvironment()) {
    return { blocked: false };
  }

  console.log('🦊 Testing Supabase connectivity for Firefox content blocking...');

  try {
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    if (!supabaseUrl) {
      return { blocked: false, error: 'No Supabase URL configured' };
    }

    // Test basic REST API connectivity
    const testUrl = `${supabaseUrl}/rest/v1/`;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    try {
      const response = await fetch(testUrl, {
        method: 'HEAD',
        signal: controller.signal,
        headers: {
          'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY || '',
        }
      });

      clearTimeout(timeoutId);

      // If we get a response (even if it's an error), the connection isn't blocked
      console.log('🦊 Supabase REST API test response:', response.status);
      return { blocked: false };

    } catch (fetchError: any) {
      clearTimeout(timeoutId);

      const errorMessage = fetchError.message || String(fetchError);
      console.log('🦊 Supabase connectivity test failed:', errorMessage);

      // Check for Firefox-specific blocking errors
      const blockingPatterns = [
        /NS_ERROR_CONTENT_BLOCKED/i,
        /content.*blocked/i,
        /blocked.*request/i,
        /network.*error/i,
        /failed.*fetch/i
      ];

      const isBlocked = blockingPatterns.some(pattern => pattern.test(errorMessage));

      if (isBlocked) {
        console.log('🦊 Firefox content blocking detected!');
        return {
          blocked: true,
          error: errorMessage,
          details: {
            errorName: fetchError.name,
            errorCode: fetchError.code,
            userAgent: navigator.userAgent
          }
        };
      }

      return { blocked: false, error: errorMessage };
    }

  } catch (error: any) {
    console.error('🦊 Error testing Supabase connectivity:', error);
    return { blocked: false, error: error.message };
  }
}

/**
 * Initialize Firefox content blocking detection
 */
export function initializeFirefoxContentBlockingDetection(): void {
  if (!isFirefoxBrowser() || !isProductionEnvironment()) {
    return;
  }

  console.log('🦊 Initializing Firefox content blocking detection...');

  // Test connectivity on page load
  setTimeout(async () => {
    const result = await testSupabaseConnectivity();
    
    if (result.blocked) {
      console.log('🦊 Content blocking detected, enabling fallback mode');
      localStorage.setItem('firefox-content-blocked', 'true');
      localStorage.setItem('firefox-use-fallback-auth', 'true');
      
      // Dispatch custom event to notify components
      window.dispatchEvent(new CustomEvent('firefox-content-blocked', {
        detail: result
      }));
    } else {
      console.log('🦊 No content blocking detected');
      // Clear any previous fallback flags
      localStorage.removeItem('firefox-content-blocked');
      localStorage.removeItem('firefox-use-fallback-auth');
    }
  }, 1000); // Wait 1 second after page load
}

/**
 * Check if Firefox fallback mode should be used
 */
export function shouldUseFirefoxFallback(): boolean {
  return isFirefoxBrowser() && 
         isProductionEnvironment() && 
         (localStorage.getItem('firefox-use-fallback-auth') === 'true' ||
          localStorage.getItem('firefox-content-blocked') === 'true');
}

/**
 * Enable Firefox fallback mode manually
 */
export function enableFirefoxFallback(reason: string = 'Manual activation'): void {
  if (!isFirefoxBrowser()) {
    console.log('🦊 Not Firefox browser, ignoring fallback activation');
    return;
  }

  console.log('🦊 Enabling Firefox fallback mode:', reason);
  localStorage.setItem('firefox-use-fallback-auth', 'true');
  localStorage.setItem('firefox-fallback-reason', reason);
  
  // Dispatch event
  window.dispatchEvent(new CustomEvent('firefox-fallback-enabled', {
    detail: { reason }
  }));
}

/**
 * Disable Firefox fallback mode
 */
export function disableFirefoxFallback(): void {
  console.log('🦊 Disabling Firefox fallback mode');
  localStorage.removeItem('firefox-use-fallback-auth');
  localStorage.removeItem('firefox-content-blocked');
  localStorage.removeItem('firefox-fallback-reason');
  
  // Dispatch event
  window.dispatchEvent(new CustomEvent('firefox-fallback-disabled'));
}

/**
 * Get Firefox fallback status
 */
export function getFirefoxFallbackStatus(): {
  enabled: boolean;
  reason?: string;
  contentBlocked: boolean;
} {
  return {
    enabled: shouldUseFirefoxFallback(),
    reason: localStorage.getItem('firefox-fallback-reason') || undefined,
    contentBlocked: localStorage.getItem('firefox-content-blocked') === 'true'
  };
}
