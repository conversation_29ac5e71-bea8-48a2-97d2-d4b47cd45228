/**
 * Firefox-specific authentication handler
 * Addresses Firefox PWA and service worker compatibility issues
 */

import { isFirefox, shouldUseFirefoxAuthFallback } from './compatibilityChecker';
import { productionFirefoxFix } from './productionFirefoxFix';

export interface FirefoxAuthOptions {
  disableServiceWorker?: boolean;
  clearCacheOnAuth?: boolean;
  useAlternativeStorage?: boolean;
  enableDebugLogging?: boolean;
  isProduction?: boolean;
}

class FirefoxAuthHandler {
  private options: FirefoxAuthOptions;
  private debugLog: string[] = [];

  constructor(options: FirefoxAuthOptions = {}) {
    // Detect if we're in production environment
    const isProduction = import.meta.env.PROD || window.location.protocol === 'https:';

    this.options = {
      disableServiceWorker: true, // Disable SW for Firefox by default
      clearCacheOnAuth: true,     // Clear cache before auth attempts
      useAlternativeStorage: true, // Use sessionStorage instead of localStorage for some data
      enableDebugLogging: true,   // Enable detailed logging
      isProduction,               // Auto-detect production environment
      ...options
    };

    if (this.options.enableDebugLogging) {
      console.log('🦊 Firefox Auth Handler initialized with options:', this.options);
      console.log('🦊 Environment detected:', isProduction ? 'Production' : 'Development');
    }
  }

  /**
   * Checks if Firefox-specific handling is needed
   */
  public shouldUseFirefoxHandling(): boolean {
    return shouldUseFirefoxAuthFallback();
  }

  /**
   * Prepares Firefox for authentication
   */
  public async prepareForAuthentication(): Promise<void> {
    if (!this.shouldUseFirefoxHandling()) {
      this.log('🦊 Skipping Firefox preparation - not Firefox or not needed');
      this.log(`🦊 Debug info: isFirefox=${isFirefox()}, shouldUseFirefoxAuthFallback=${shouldUseFirefoxAuthFallback()}`);
      return;
    }

    this.log('🔧 Preparing Firefox for authentication...');
    this.log(`🔧 Firefox version: ${navigator.userAgent}`);
    this.log(`🔧 Production mode: ${this.options.isProduction}`);
    this.log(`🔧 Current URL: ${window.location.href}`);
    this.log(`🔧 Protocol: ${window.location.protocol}`);

    try {
      // Set authentication in progress flag
      localStorage.setItem('datastatpro-auth-in-progress', 'true');
      this.log('🔧 Set authentication in progress flag');

      // Clear problematic caches
      if (this.options.clearCacheOnAuth) {
        this.log('🔧 Starting cache clearing...');
        await this.clearProblematicCaches();
        this.log('🔧 Cache clearing completed');
      }

      // Disable service worker if needed
      if (this.options.disableServiceWorker) {
        this.log('🔧 Starting service worker disable...');
        await this.disableServiceWorkerForAuth();
        this.log('🔧 Service worker disable completed');
      }

      // Clear authentication-related storage
      this.log('🔧 Clearing auth storage...');
      this.clearAuthStorage();
      this.log('🔧 Auth storage cleared');

      // Add Firefox-specific network preparation
      await this.prepareFirefoxNetworking();

      this.log('✅ Firefox preparation completed successfully');
    } catch (error) {
      this.log(`❌ Firefox preparation failed: ${error}`);
      this.log(`❌ Error stack: ${error instanceof Error ? error.stack : 'No stack trace'}`);
      throw error;
    }
  }

  /**
   * Clears caches that may interfere with authentication
   */
  private async clearProblematicCaches(): Promise<void> {
    try {
      if ('caches' in window) {
        const cacheNames = await caches.keys();

        if (this.options.isProduction) {
          // In production, clear ALL caches for Firefox to prevent conflicts
          this.log('🦊 Production environment - clearing ALL caches for Firefox');

          for (const cacheName of cacheNames) {
            await caches.delete(cacheName);
            this.log(`🗑️ Cleared production cache: ${cacheName}`);
          }
        } else {
          // In development, clear specific caches that may cause issues
          const problematicCaches = cacheNames.filter(name =>
            name.includes('api-cache') ||
            name.includes('pages-cache') ||
            name.includes('workbox')
          );

          for (const cacheName of problematicCaches) {
            await caches.delete(cacheName);
            this.log(`🗑️ Cleared development cache: ${cacheName}`);
          }
        }
      }
    } catch (error) {
      this.log(`⚠️ Cache clearing failed: ${error}`);
    }
  }

  /**
   * Temporarily disables service worker for authentication
   */
  private async disableServiceWorkerForAuth(): Promise<void> {
    try {
      if ('serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations();

        if (this.options.isProduction) {
          // In production, completely unregister service workers for Firefox
          this.log('🦊 Production environment detected - unregistering service workers');

          for (const registration of registrations) {
            await registration.unregister();
            this.log(`🚫 Unregistered service worker: ${registration.scope}`);
          }

          // Mark that we unregistered for production
          localStorage.setItem('firefox-sw-unregistered-for-auth', 'true');
        } else {
          // In development, just mark for temporary disable
          for (const registration of registrations) {
            localStorage.setItem('firefox-sw-disabled-for-auth', 'true');
            this.log('🚫 Service worker marked for temporary disable');
          }
        }
      }
    } catch (error) {
      this.log(`⚠️ Service worker disable failed: ${error}`);
    }
  }

  /**
   * Clears authentication-related storage
   */
  private clearAuthStorage(): void {
    try {
      // Clear specific localStorage keys that may cause issues
      const authKeys = [
        'datastatpro-auth-loading-stuck',
        'datastatpro-cache-corruption-detected',
        'datastatpro-loading-failures'
      ];

      authKeys.forEach(key => {
        localStorage.removeItem(key);
        this.log(`🗑️ Cleared storage key: ${key}`);
      });

      // Clear session storage auth flags
      sessionStorage.removeItem('isGuest');
      sessionStorage.removeItem('showSignupSuccess');

    } catch (error) {
      this.log(`⚠️ Auth storage clearing failed: ${error}`);
    }
  }

  /**
   * Prepares Firefox networking for authentication
   */
  private async prepareFirefoxNetworking(): Promise<void> {
    try {
      this.log('🔧 Preparing Firefox networking...');

      // Clear DNS cache by making a preflight request
      if (this.options.isProduction) {
        try {
          const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
          if (supabaseUrl) {
            this.log(`🔧 Making preflight request to: ${supabaseUrl}`);
            await fetch(`${supabaseUrl}/rest/v1/`, {
              method: 'HEAD',
              mode: 'cors',
              cache: 'no-cache',
              headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache'
              }
            }).catch(error => {
              this.log(`🔧 Preflight request failed (expected): ${error.message}`);
            });
          }
        } catch (error) {
          this.log(`🔧 Preflight request error: ${error}`);
        }
      }

      // Set Firefox-specific fetch options
      if (window.fetch) {
        const originalFetch = window.fetch;
        window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
          // Add Firefox-specific headers for auth requests
          if (typeof input === 'string' && input.includes('supabase')) {
            const modifiedInit = {
              ...init,
              headers: {
                ...init?.headers,
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
              }
            };
            this.log(`🔧 Modified fetch for Supabase request: ${input}`);
            return originalFetch(input, modifiedInit);
          }
          return originalFetch(input, init);
        };
      }

      this.log('✅ Firefox networking preparation completed');
    } catch (error) {
      this.log(`⚠️ Firefox networking preparation failed: ${error}`);
    }
  }

  /**
   * Handles post-authentication cleanup
   */
  public async handlePostAuthentication(): Promise<void> {
    if (!this.shouldUseFirefoxHandling()) return;

    this.log('🔧 Handling post-authentication cleanup...');

    try {
      // Clear authentication in progress flag
      localStorage.removeItem('datastatpro-auth-in-progress');

      if (this.options.isProduction) {
        // In production, check if we unregistered service workers
        if (localStorage.getItem('firefox-sw-unregistered-for-auth') === 'true') {
          localStorage.removeItem('firefox-sw-unregistered-for-auth');
          this.log('✅ Production service worker cleanup completed');

          // Don't re-register service workers in production for Firefox
          // Let the user navigate normally without PWA interference
          this.log('🦊 Service workers will remain disabled for Firefox in production');
        }

        // Signal authentication success to other tabs
        productionFirefoxFix.signalAuthenticationSuccess();
      } else {
        // In development, re-enable service worker if it was disabled
        if (localStorage.getItem('firefox-sw-disabled-for-auth') === 'true') {
          localStorage.removeItem('firefox-sw-disabled-for-auth');
          this.log('✅ Development service worker re-enabled');
        }
      }

      // Clear debug logs after successful auth
      this.clearDebugLogs();

    } catch (error) {
      this.log(`❌ Post-auth cleanup failed: ${error}`);
    }
  }

  /**
   * Handles authentication errors specific to Firefox
   */
  public handleAuthenticationError(error: any): { handled: boolean; message?: string } {
    if (!this.shouldUseFirefoxHandling()) {
      return { handled: false };
    }

    this.log(`🚨 Handling Firefox auth error: ${error?.message || error}`);
    this.log(`🚨 Error details:`, {
      message: error?.message,
      name: error?.name,
      stack: error?.stack,
      type: typeof error,
      stringified: String(error)
    });

    const errorMessage = error?.message || String(error);
    const errorStack = error?.stack || '';
    const fullErrorText = `${errorMessage} ${errorStack}`;

    // First, check if this is actually an authentication success that shouldn't be treated as an error
    const successPatterns = [
      /success/i,
      /complete/i,
      /authenticated/i,
      /signed.?in/i,
      /logged.?in/i
    ];

    const isSuccess = successPatterns.some(pattern => pattern.test(fullErrorText));
    if (isSuccess) {
      this.log(`🚨 This appears to be a success message, not an error: ${errorMessage}`);
      return { handled: false };
    }

    // Check for Firefox-specific error patterns that actually need handling
    const firefoxErrorPatterns = [
      /service worker/i,
      /cache/i,
      /network/i,
      /cors/i,
      /security/i,
      /blocked/i,
      /failed to fetch/i,
      /load failed/i,
      /script error/i,
      /module.*not.*found/i
    ];

    // Don't handle normal authentication errors - let them be handled normally
    const normalAuthErrorPatterns = [
      /invalid.*credential/i,
      /wrong.*password/i,
      /user.*not.*found/i,
      /email.*not.*confirmed/i,
      /invalid.*email/i,
      /password.*too.*short/i
    ];

    const isNormalAuthError = normalAuthErrorPatterns.some(pattern => pattern.test(fullErrorText));
    if (isNormalAuthError) {
      this.log(`🚨 This is a normal authentication error, not a Firefox-specific issue: ${errorMessage}`);
      return { handled: false };
    }

    const isFirefoxSpecific = firefoxErrorPatterns.some(pattern =>
      pattern.test(fullErrorText)
    );

    this.log(`🚨 Firefox-specific error check: ${isFirefoxSpecific}, patterns matched: ${firefoxErrorPatterns.filter(p => p.test(fullErrorText)).map(p => p.source)}`);

    if (isFirefoxSpecific) {
      return {
        handled: true,
        message: 'Firefox detected a security or caching issue. Please try refreshing the page or clearing your browser cache.'
      };
    }

    return { handled: false };
  }

  /**
   * Handles silent authentication failures specific to Firefox
   */
  public handleSilentAuthFailure(): void {
    if (!this.shouldUseFirefoxHandling()) return;

    this.log('🦊 Handling silent authentication failure...');

    // Clear all authentication-related storage
    this.clearAuthStorage();

    // Clear all caches
    if (this.options.isProduction) {
      this.clearProblematicCaches().then(() => {
        this.log('🦊 Caches cleared after silent failure');
      }).catch(error => {
        this.log(`🦊 Cache clearing failed: ${error}`);
      });
    }

    // Set a flag to indicate silent failure occurred
    localStorage.setItem('firefox-silent-auth-failure', 'true');

    this.log('🦊 Silent authentication failure handling completed');
  }

  /**
   * Gets Firefox-specific debugging information
   */
  public getDebugInfo(): object {
    return {
      isFirefox: isFirefox(),
      shouldUseFirefoxHandling: this.shouldUseFirefoxHandling(),
      options: this.options,
      debugLogs: this.debugLog.slice(-10), // Last 10 log entries
      serviceWorkerDisabled: localStorage.getItem('firefox-sw-disabled-for-auth') === 'true',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Logs debug information
   */
  private log(message: string): void {
    if (this.options.enableDebugLogging) {
      console.log(`[Firefox Auth Handler] ${message}`);
      this.debugLog.push(`${new Date().toISOString()}: ${message}`);
      
      // Keep only last 50 log entries
      if (this.debugLog.length > 50) {
        this.debugLog = this.debugLog.slice(-50);
      }
    }
  }

  /**
   * Clears debug logs
   */
  private clearDebugLogs(): void {
    this.debugLog = [];
  }

  /**
   * Forces a clean reload for Firefox
   */
  public async forceCleanReload(): Promise<void> {
    if (!this.shouldUseFirefoxHandling()) return;

    this.log('🔄 Forcing clean reload for Firefox...');

    try {
      // Clear all caches
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(cacheNames.map(name => caches.delete(name)));
      }

      // Clear all storage
      localStorage.clear();
      sessionStorage.clear();

      // Unregister service workers
      if ('serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations();
        await Promise.all(registrations.map(reg => reg.unregister()));
      }

      // Force reload
      window.location.reload();
    } catch (error) {
      this.log(`❌ Clean reload failed: ${error}`);
      // Fallback to simple reload
      window.location.reload();
    }
  }
}

// Export singleton instance
export const firefoxAuthHandler = new FirefoxAuthHandler();

// Export class for custom instances
export { FirefoxAuthHandler };
