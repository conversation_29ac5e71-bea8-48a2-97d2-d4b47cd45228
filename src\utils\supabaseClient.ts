import { createClient } from '@supabase/supabase-js';

// Initialize the Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

if (!supabaseUrl || !supabaseAnonKey) {
  console.warn('Supabase credentials are missing. Authentication features will not work properly.');
}

// Firefox detection
function isFirefoxBrowser(): boolean {
  return /Firefox/.test(navigator.userAgent);
}

// Production environment detection
function isProductionEnvironment(): boolean {
  const isHttps = window.location.protocol === 'https:';
  const isProductionDomain = window.location.hostname === 'datastatpro.com' ||
                            window.location.hostname.includes('datastatpro');
  const hasImportMetaProd = typeof import.meta !== 'undefined' && import.meta.env?.PROD;

  return isHttps || isProductionDomain || hasImportMetaProd;
}

// Check if we should use fallback mode for Firefox
function shouldUseFirefoxFallback(): boolean {
  return isFirefoxBrowser() &&
         isProductionEnvironment() &&
         (localStorage.getItem('firefox-use-fallback-auth') === 'true' ||
          localStorage.getItem('firefox-content-blocked') === 'true');
}

// Create Supabase client with Firefox-specific configuration
const createSupabaseClient = () => {
  const useFirefoxFallback = shouldUseFirefoxFallback();

  if (useFirefoxFallback) {
    console.log('🦊 Creating Supabase client with Firefox fallback configuration (real-time disabled)');

    return createClient(supabaseUrl, supabaseAnonKey, {
      realtime: {
        // Disable real-time features for Firefox to prevent content blocking
        params: {
          eventsPerSecond: 0
        }
      },
      auth: {
        // Use more conservative auth settings for Firefox
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: false // Disable URL-based session detection that might cause issues
      }
    });
  }

  console.log('🔗 Creating standard Supabase client');
  return createClient(supabaseUrl, supabaseAnonKey);
};

export const supabase = createSupabaseClient();

// Export function to recreate client if needed
export const recreateSupabaseClient = () => {
  console.log('🔄 Recreating Supabase client...');
  return createSupabaseClient();
};