import { createClient } from '@supabase/supabase-js';

// Initialize the Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

if (!supabaseUrl || !supabaseAnonKey) {
  console.warn('Supabase credentials are missing. Authentication features will not work properly.');
}

// Firefox detection
function isFirefoxBrowser(): boolean {
  return /Firefox/.test(navigator.userAgent);
}

// Production environment detection
function isProductionEnvironment(): boolean {
  const isHttps = window.location.protocol === 'https:';
  const isProductionDomain = window.location.hostname === 'datastatpro.com' ||
                            window.location.hostname.includes('datastatpro');
  const hasImportMetaProd = typeof import.meta !== 'undefined' && import.meta.env?.PROD;

  return isHttps || isProductionDomain || hasImportMetaProd;
}

// Check if we should use fallback mode for Firefox
function shouldUseFirefoxFallback(): boolean {
  // Always use fallback for Firefox in production to prevent NS_ERROR_CONTENT_BLOCKED
  if (isFirefoxBrowser() && isProductionEnvironment()) {
    console.log('🦊 Firefox in production detected - using fallback mode to prevent content blocking');
    return true;
  }

  // Also check for explicit fallback flags
  return localStorage.getItem('firefox-use-fallback-auth') === 'true' ||
         localStorage.getItem('firefox-content-blocked') === 'true';
}

// Create Supabase client with Firefox-specific configuration
const createSupabaseClient = () => {
  const useFirefoxFallback = shouldUseFirefoxFallback();

  if (useFirefoxFallback) {
    console.log('🦊 Creating Supabase client with Firefox fallback configuration (real-time disabled)');

    return createClient(supabaseUrl, supabaseAnonKey, {
      realtime: {
        // Completely disable real-time features for Firefox
        params: {
          eventsPerSecond: 0
        },
        // Additional real-time disabling options
        heartbeatIntervalMs: 0,
        reconnectAfterMs: () => null, // Disable reconnection attempts
      },
      auth: {
        // Use more conservative auth settings for Firefox
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: false, // Disable URL-based session detection that might cause issues
        // Disable real-time auth state changes
        flowType: 'pkce'
      },
      // Global settings to prevent any real-time connections
      global: {
        headers: {
          'X-Firefox-Fallback': 'true'
        }
      }
    });
  }

  console.log('🔗 Creating standard Supabase client');
  return createClient(supabaseUrl, supabaseAnonKey);
};

export const supabase = createSupabaseClient();

// Export function to recreate client if needed
export const recreateSupabaseClient = () => {
  console.log('🔄 Recreating Supabase client...');
  return createSupabaseClient();
};