/**
 * Production-specific Firefox authentication fix
 * Addresses the gap between authentication preparation and execution
 * that causes login failures in production despite successful preparation
 */

import { isFirefox } from './compatibilityChecker';

export class ProductionFirefoxAuthFix {
  private static instance: ProductionFirefoxAuthFix;
  private isProduction: boolean;
  private authenticationLock: boolean = false;
  private originalFetch: typeof window.fetch;
  private originalServiceWorkerRegister: typeof navigator.serviceWorker.register | null = null;
  private authenticationTimeout: NodeJS.Timeout | null = null;

  constructor() {
    // Consistent production detection with other Firefox utilities
    const isHttps = window.location.protocol === 'https:';
    const isProductionDomain = window.location.hostname === 'datastatpro.com' ||
                              window.location.hostname.includes('datastatpro');
    const hasImportMetaProd = typeof import.meta !== 'undefined' && import.meta.env?.PROD;

    this.isProduction = isHttps || isProductionDomain || hasImportMetaProd;
    this.originalFetch = window.fetch.bind(window);
  }

  public static getInstance(): ProductionFirefoxAuthFix {
    if (!ProductionFirefoxAuthFix.instance) {
      ProductionFirefoxAuthFix.instance = new ProductionFirefoxAuthFix();
    }
    return ProductionFirefoxAuthFix.instance;
  }

  /**
   * Initializes the production Firefox authentication fix
   */
  public initialize(): void {
    if (!isFirefox() || !this.isProduction) {
      console.log('🦊 Production Firefox Auth Fix: Skipping initialization - not Firefox or not production');
      return;
    }

    console.log('🦊 Production Firefox Auth Fix: Initializing...');
    
    // Set up persistent service worker blocking
    this.setupPersistentServiceWorkerBlocking();
    
    // Set up authentication-aware fetch interception
    this.setupAuthenticationAwareFetch();
    
    // Set up authentication state monitoring
    this.setupAuthenticationStateMonitoring();

    console.log('✅ Production Firefox Auth Fix: Initialized successfully');
  }

  /**
   * Starts authentication protection mode
   */
  public startAuthenticationProtection(): void {
    if (!isFirefox() || !this.isProduction) return;

    console.log('🦊 Production Firefox Auth Fix: Starting authentication protection');
    
    this.authenticationLock = true;
    localStorage.setItem('firefox-production-auth-lock', 'true');
    localStorage.setItem('firefox-production-auth-start', Date.now().toString());

    // Set a timeout to automatically release the lock if authentication takes too long
    this.authenticationTimeout = setTimeout(() => {
      console.warn('🦊 Production Firefox Auth Fix: Authentication timeout - releasing lock');
      this.endAuthenticationProtection();
    }, 30000); // 30 second timeout

    // Force clear any existing service worker registrations
    this.forceUnregisterServiceWorkers();

    console.log('🦊 Production Firefox Auth Fix: Authentication protection active');
  }

  /**
   * Ends authentication protection mode
   */
  public endAuthenticationProtection(): void {
    if (!isFirefox() || !this.isProduction) return;

    console.log('🦊 Production Firefox Auth Fix: Ending authentication protection');
    
    this.authenticationLock = false;
    localStorage.removeItem('firefox-production-auth-lock');
    localStorage.removeItem('firefox-production-auth-start');

    if (this.authenticationTimeout) {
      clearTimeout(this.authenticationTimeout);
      this.authenticationTimeout = null;
    }

    console.log('🦊 Production Firefox Auth Fix: Authentication protection ended');
  }

  /**
   * Sets up persistent service worker blocking during authentication
   */
  private setupPersistentServiceWorkerBlocking(): void {
    if (!('serviceWorker' in navigator)) return;

    // Store original register function
    this.originalServiceWorkerRegister = navigator.serviceWorker.register.bind(navigator.serviceWorker);

    // Override service worker registration with authentication-aware logic
    navigator.serviceWorker.register = async (scriptURL: string | URL, options?: RegistrationOptions) => {
      // Check if authentication is in progress
      if (this.authenticationLock || localStorage.getItem('firefox-production-auth-lock') === 'true') {
        console.log('🦊 Production Firefox Auth Fix: Blocking service worker registration during authentication');
        console.log('🦊 Blocked script URL:', scriptURL);
        
        // Return a mock registration that won't interfere
        return this.createMockServiceWorkerRegistration();
      }

      // Allow normal registration when not authenticating
      try {
        console.log('🦊 Production Firefox Auth Fix: Allowing service worker registration');
        return await this.originalServiceWorkerRegister!(scriptURL, {
          ...options,
          updateViaCache: 'none' as ServiceWorkerUpdateViaCache
        });
      } catch (error) {
        console.warn('🦊 Production Firefox Auth Fix: Service worker registration failed, returning mock:', error);
        return this.createMockServiceWorkerRegistration();
      }
    };
  }

  /**
   * Sets up authentication-aware fetch interception
   */
  private setupAuthenticationAwareFetch(): void {
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      const url = typeof input === 'string' ? input : input instanceof URL ? input.href : input.url;

      // Check if this is a Supabase authentication request
      if (url.includes('supabase.co') && (url.includes('auth') || url.includes('token'))) {
        console.log('🦊 Production Firefox Auth Fix: Intercepting Supabase auth request');
        
        // Add Firefox production-specific headers
        const modifiedInit = {
          ...init,
          headers: {
            ...init?.headers,
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
            // Add Firefox-specific headers
            'X-Firefox-Production': 'true',
            'X-Requested-With': 'XMLHttpRequest'
          },
          // Ensure credentials are included
          credentials: 'include' as RequestCredentials
        };

        try {
          const response = await this.originalFetch(input, modifiedInit);
          console.log('🦊 Production Firefox Auth Fix: Supabase auth request completed', {
            status: response.status,
            statusText: response.statusText,
            ok: response.ok
          });
          return response;
        } catch (error) {
          console.error('🦊 Production Firefox Auth Fix: Supabase auth request failed:', error);
          throw error;
        }
      }

      // For non-auth requests, use original fetch
      return this.originalFetch(input, init);
    };
  }

  /**
   * Sets up authentication state monitoring
   */
  private setupAuthenticationStateMonitoring(): void {
    // Monitor for authentication state changes
    window.addEventListener('storage', (event) => {
      if (event.key === 'firefox-production-auth-success' && event.newValue === 'true') {
        console.log('🦊 Production Firefox Auth Fix: Authentication success detected');
        
        // End authentication protection
        this.endAuthenticationProtection();
        
        // Clear the success flag
        localStorage.removeItem('firefox-production-auth-success');
        
        // Signal success to other components
        window.dispatchEvent(new CustomEvent('firefox-production-auth-completed', {
          detail: { success: true }
        }));
      }

      if (event.key === 'firefox-production-auth-failure' && event.newValue === 'true') {
        console.log('🦊 Production Firefox Auth Fix: Authentication failure detected');
        
        // End authentication protection
        this.endAuthenticationProtection();
        
        // Clear the failure flag
        localStorage.removeItem('firefox-production-auth-failure');
        
        // Signal failure to other components
        window.dispatchEvent(new CustomEvent('firefox-production-auth-completed', {
          detail: { success: false }
        }));
      }
    });

    // Monitor for stuck authentication (cleanup mechanism)
    setInterval(() => {
      const authStartTime = localStorage.getItem('firefox-production-auth-start');
      if (authStartTime && this.authenticationLock) {
        const elapsed = Date.now() - parseInt(authStartTime);
        
        // If authentication has been locked for more than 60 seconds, force release
        if (elapsed > 60000) {
          console.warn('🦊 Production Firefox Auth Fix: Forcing authentication lock release due to timeout');
          this.endAuthenticationProtection();
        }
      }
    }, 10000); // Check every 10 seconds
  }

  /**
   * Forces unregistration of all service workers
   */
  private async forceUnregisterServiceWorkers(): Promise<void> {
    if (!('serviceWorker' in navigator)) return;

    try {
      const registrations = await navigator.serviceWorker.getRegistrations();
      console.log(`🦊 Production Firefox Auth Fix: Found ${registrations.length} service worker registrations to unregister`);
      
      for (const registration of registrations) {
        await registration.unregister();
        console.log('🦊 Production Firefox Auth Fix: Unregistered service worker:', registration.scope);
      }
    } catch (error) {
      console.warn('🦊 Production Firefox Auth Fix: Error unregistering service workers:', error);
    }
  }

  /**
   * Creates a mock service worker registration
   */
  private createMockServiceWorkerRegistration(): Promise<ServiceWorkerRegistration> {
    return Promise.resolve({
      scope: '/',
      installing: null,
      waiting: null,
      active: null,
      onupdatefound: null,
      updateViaCache: 'imports' as ServiceWorkerUpdateViaCache,
      navigationPreload: {
        enable: () => Promise.resolve(),
        disable: () => Promise.resolve(),
        setHeaderValue: () => Promise.resolve(),
        getState: () => Promise.resolve({ enabled: false, headerValue: '' })
      },
      pushManager: {
        supportedContentEncodings: [],
        subscribe: () => Promise.reject(new Error('Push not supported during Firefox authentication')),
        getSubscription: () => Promise.resolve(null),
        permissionState: () => Promise.resolve('denied' as PermissionState)
      },
      sync: {
        register: () => Promise.resolve(),
        getTags: () => Promise.resolve([])
      },
      getNotifications: () => Promise.resolve([]),
      showNotification: () => Promise.resolve(),
      addEventListener: () => {},
      removeEventListener: () => {},
      dispatchEvent: () => false,
      unregister: () => Promise.resolve(true),
      update: () => Promise.resolve()
    } as ServiceWorkerRegistration);
  }

  /**
   * Signals authentication success
   */
  public signalAuthenticationSuccess(): void {
    if (!isFirefox() || !this.isProduction) return;

    console.log('🦊 Production Firefox Auth Fix: Signaling authentication success');
    localStorage.setItem('firefox-production-auth-success', 'true');
    
    // Remove the signal after a brief delay
    setTimeout(() => {
      localStorage.removeItem('firefox-production-auth-success');
    }, 2000);
  }

  /**
   * Signals authentication failure
   */
  public signalAuthenticationFailure(): void {
    if (!isFirefox() || !this.isProduction) return;

    console.log('🦊 Production Firefox Auth Fix: Signaling authentication failure');
    localStorage.setItem('firefox-production-auth-failure', 'true');
    
    // Remove the signal after a brief delay
    setTimeout(() => {
      localStorage.removeItem('firefox-production-auth-failure');
    }, 2000);
  }

  /**
   * Gets debug information
   */
  public getDebugInfo(): object {
    return {
      isFirefox: isFirefox(),
      isProduction: this.isProduction,
      authenticationLock: this.authenticationLock,
      hasTimeout: this.authenticationTimeout !== null,
      authFlags: {
        'firefox-production-auth-lock': localStorage.getItem('firefox-production-auth-lock'),
        'firefox-production-auth-start': localStorage.getItem('firefox-production-auth-start'),
        'firefox-production-auth-success': localStorage.getItem('firefox-production-auth-success'),
        'firefox-production-auth-failure': localStorage.getItem('firefox-production-auth-failure')
      },
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Cleanup method
   */
  public cleanup(): void {
    if (!isFirefox() || !this.isProduction) return;

    console.log('🦊 Production Firefox Auth Fix: Cleaning up...');
    
    // End authentication protection
    this.endAuthenticationProtection();
    
    // Restore original service worker register if we have it
    if (this.originalServiceWorkerRegister && 'serviceWorker' in navigator) {
      navigator.serviceWorker.register = this.originalServiceWorkerRegister;
    }
    
    // Restore original fetch
    window.fetch = this.originalFetch;
    
    // Clear all auth flags
    const authFlags = [
      'firefox-production-auth-lock',
      'firefox-production-auth-start',
      'firefox-production-auth-success',
      'firefox-production-auth-failure'
    ];
    
    authFlags.forEach(flag => localStorage.removeItem(flag));
    
    console.log('✅ Production Firefox Auth Fix: Cleanup completed');
  }
}

// Export singleton instance
export const productionFirefoxAuthFix = ProductionFirefoxAuthFix.getInstance();