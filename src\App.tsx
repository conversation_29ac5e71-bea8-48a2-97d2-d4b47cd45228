import React, { useState, useEffect, ReactNode, Suspense, lazy, useCallback } from 'react';
import { useAuth } from './context/AuthContext';
import { useData } from './context/DataContext';
import { Box, Snackbar, Alert, useMediaQuery, useTheme, CircularProgress, Button, Typography } from '@mui/material';
import { UpdateNotification } from './components/PWA'; // Import enhanced PWA components
import AppHeader from './components/Layout/AppHeader';
import AuthAppHeader from './components/Layout/AuthAppHeader';
import Sidebar from './components/Layout/Sidebar';
import MainContent from './components/Layout/MainContent';
import AppRouter from './routing/AppRouter';
import { initializeFirefoxContentBlockingDetection } from './utils/firefoxContentBlockingDetector';
// import HomePage from './pages/HomePage'; // Lazy load
// import DataManagement from './components/DataManagement'; // Lazy load
// import DescriptiveStats from './components/DescriptiveStats'; // Lazy load
// import InferentialStats from './components/InferentialStats'; // Lazy load
// import CorrelationAnalysis from './components/CorrelationAnalysis'; // Lazy load
// import Visualization from './components/Visualization'; // Lazy load
// import TTestWorkflowPage from './pages/TTestWorkflowPage'; // Lazy load
// import ANOVAWorkflowPage from './pages/ANOVAWorkflowPage'; // Lazy load
// import AnalysisAssistantPage from './pages/AnalysisAssistantPage'; // Lazy load
// import WhichTestPage from './pages/WhichTestPage'; // Lazy load
// import StatisticalMethodsPage from './pages/StatisticalMethodsPage'; // Lazy load
// import Auth, { AuthView } from './components/Auth/Auth'; // Lazy load
// import UserProfile from './components/Auth/UserProfile'; // Lazy load
// import Settings from './components/Auth/Settings'; // Lazy load
// import UpdatePassword from './components/Auth/UpdatePassword'; // Lazy load
import ResponsiveTester from './components/DevTools/ResponsiveTester';
import DatasetNameConflictDialog from './components/DataManagement/DatasetNameConflictDialog'; // Import the dialog
// import PivotAnalysisViewer from './components/PivotAnalysis/PivotAnalysisViewer'; // Lazy load
import { AuthView } from './components/Auth/AuthContainer'; // Import AuthView from its source

// Lazy load components
const HomePage = lazy(() => import('./pages/HomePage'));
const DataManagement = lazy(() => import('./components/DataManagement')); // Re-add lazy import for DataManagement component
const DescriptiveStats = lazy(() => import('./components/DescriptiveStats'));
const InferentialStats = lazy(() => import('./components/InferentialStats'));
const CorrelationAnalysis = lazy(() => import('./components/CorrelationAnalysis'));
const Visualization = lazy(() => import('./components/Visualization'));
const TTestWorkflowPage = lazy(() => import('./pages/TTestWorkflowPage'));
const ANOVAWorkflowPage = lazy(() => import('./pages/ANOVAWorkflowPage'));
const ANOVA = lazy(() => import('./components/InferentialStats/ANOVA'));
const RepeatedMeasuresANOVAWorkflowPage = lazy(() => import('./pages/RepeatedMeasuresANOVAWorkflowPage')); // Add lazy import
const AnalysisAssistantPage = lazy(() => import('./pages/AnalysisAssistantPage'));
const WhichTestPage = lazy(() => import('./pages/WhichTestPage'));
const VisualizationGuidePage = lazy(() => import('./pages/VisualizationGuidePage'));
const StatisticalMethodsPage = lazy(() => import('./pages/StatisticalMethodsPage'));
const Auth = lazy(() => import('./components/Auth/Auth'));
const DashboardPage = lazy(() => import('./pages/DashboardPage')); // Lazy load DashboardPage
const DataVisualizationPage = lazy(() => import('./pages/DataVisualizationPage')); // Lazy load DataVisualizationPage
const PublicationReadyPage = lazy(() => import('./pages/PublicationReadyPage')); // Lazy load PublicationReadyPage
const UserProfile = lazy(() => import('./components/Auth/UserProfile'));
const Settings = lazy(() => import('./components/Auth/Settings'));
const UpdatePassword = lazy(() => import('./components/Auth/UpdatePassword'));
const PivotAnalysisViewer = lazy(() => import('./components/PivotAnalysis/PivotAnalysisViewer'));
const VideoTutorialsPage = lazy(() => import('./pages/VideoTutorialsPage')); // Lazy load VideoTutorialsPage
const EpiCalcPage = lazy(() => import('./pages/EpiCalcPage')); // Lazy load EpiCalcPage
const CaseControlCalculator = lazy(() => import('./components/EpiCalc/CaseControlCalculator')); // Lazy load CaseControlCalculator
const CohortCalculator = lazy(() => import('./components/EpiCalc/CohortCalculator')); // Lazy load CohortCalculator
const CrossSectionalCalculator = lazy(() => import('./components/EpiCalc/CrossSectionalCalculator')); // Lazy load CrossSectionalCalculator
const MatchedCaseControlCalculator = lazy(() => import('./components/EpiCalc/MatchedCaseControlCalculator')); // Lazy load MatchedCaseControlCalculator
const SampleSizePowerCalculator = lazy(() => import('./components/EpiCalc/SampleSizePowerCalculator')); // Lazy load SampleSizePowerCalculator
const SampleSizeCalculatorsOptions = lazy(() => import('./components/SampleSizeCalculators/SampleSizeCalculatorsOptions')); // Lazy load SampleSizeCalculatorsOptions
const OneSampleCalculator = lazy(() => import('./components/SampleSizeCalculators/OneSampleCalculator'));
const TwoSampleCalculator = lazy(() => import('./components/SampleSizeCalculators/TwoSampleCalculator'));
const PairedSampleCalculator = lazy(() => import('./components/SampleSizeCalculators/PairedSampleCalculator'));
const MoreThanTwoGroupsCalculator = lazy(() => import('./components/SampleSizeCalculators/MoreThanTwoGroupsCalculator'));
const SurvivalAnalysis = lazy(() => import('./components/AdvancedAnalysisAliases/SurvivalAnalysis')); // Lazy load SurvivalAnalysis
const ReliabilityAnalysis = lazy(() => import('./components/AdvancedAnalysisAliases/ReliabilityAnalysis')); // Lazy load ReliabilityAnalysis
const MediationModeration = lazy(() => import('./components/AdvancedAnalysisAliases/MediationModeration')); // Lazy load MediationModeration
const Table1Page = lazy(() => import('./pages/Table1Page')); // Lazy load Table1Page
const Table2Page = lazy(() => import('./pages/Table2Page')); // Lazy load Table2Page
const Table1a = lazy(() => import('./components/PublicationReady/Table1a')); // Lazy load Table1a
const SMDTable = lazy(() => import('./components/PublicationReady/SMDTable')); // Lazy load SMDTable
const FlowDiagramPage = lazy(() => import('./pages/FlowDiagramPage')); // Lazy load FlowDiagramPage
const RegressionTablePage = lazy(() => import('./pages/RegressionTablePage')); // Lazy load RegressionTablePage
const ConvertToAPA = lazy(() => import('./components/PublicationReady/ConvertToAPA')); // Lazy load ConvertToAPA
const DataManagementPage = lazy(() => import('./pages/DataManagementPage')); // Lazy load DataManagementPage
const AdvancedAnalysisPage = lazy(() => import('./pages/AdvancedAnalysisPage')); // Lazy load AdvancedAnalysisPage
const CorrelationAnalysisPage = lazy(() => import('./pages/CorrelationAnalysisPage')); // Lazy load CorrelationAnalysisPage
const ExploratoryFactorAnalysis = lazy(() => import('./components/AdvancedAnalysisAliases/ExploratoryFactorAnalysis'));
const ConfirmatoryFactorAnalysis = lazy(() => import('./components/AdvancedAnalysisAliases/ConfirmatoryFactorAnalysis')); // Lazy load ConfirmatoryFactorAnalysis
const MetaAnalysis = lazy(() => import('./components/AdvancedAnalysisAliases/MetaAnalysis')); // Lazy load MetaAnalysis
const ClusterAnalysis = lazy(() => import('./components/AdvancedAnalysisAliases/ClusterAnalysis')); // Lazy load ClusterAnalysis
const InferentialStatsPage = lazy(() => import('./pages/InferentialStatsPage')); // Lazy load InferentialStatsPage
const DescriptiveStatsPage = lazy(() => import('./pages/DescriptiveStatsPage')); // Lazy load DescriptiveStatsPage
const AnalysisIndexPage = lazy(() => import('./pages/AnalysisIndexPage')); // Lazy load AnalysisIndexPage
const KnowledgeBasePage = lazy(() => import('./pages/KnowledgeBasePage')); // Lazy load KnowledgeBasePage
const TutorialPage = lazy(() => import('./pages/TutorialPage')); // Lazy load TutorialPage
const PostHocTestsPage = lazy(() => import('./pages/PostHocTestsPage')); // Lazy load PostHocTestsPage
// Removed lazy imports for non-existent TTest pages
const NonParametricTests = lazy(() => import('./components/InferentialStats/NonParametricTests')); // Lazy load NonParametricTests
const OneWayANOVA = lazy(() => import('./components/InferentialStats/ANOVA/OneWayANOVA')); // Lazy load OneWayANOVA
const RepeatedMeasuresANOVA = lazy(() => import('./components/InferentialStats/ANOVA/RepeatedMeasuresANOVA').then(module => ({ default: module.RepeatedMeasuresANOVA }))); // Lazy load RepeatedMeasuresANOVA with named export
const TwoWayANOVA = lazy(() => import('./components/InferentialStats/ANOVA/TwoWayANOVA')); // Lazy load TwoWayANOVA
const TTests = lazy(() => import('./components/InferentialStats/TTests')); // Lazy load the main TTests component


// Determine if we should show dev tools
const isDevelopment = process.env.NODE_ENV === 'development';

// AuthView is now imported from AuthContainer.tsx

function App() {
  const { user, isGuest, logoutGuest, loading: authLoading, showSignupSuccessMessage, clearSignupSuccessMessage } = useAuth();
  const [open, setOpen] = useState(true);
  // PWA update system is now handled by the UpdateNotification component
  const [activePage, setActivePage] = useState('home');
  const [activeSubPage, setActiveSubPage] = useState('');
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'info' | 'warning' | 'error';
    actions?: ReactNode;
  }>({
    open: false,
    message: '',
    severity: 'info',
  });
  const [loading, setLoading] = useState(false);
  const { dataLoadEventInfo, clearDataLoadEvent, currentDataset } = useData();

  // Initialize routes before any navigation logic
  const [routesInitialized, setRoutesInitialized] = useState(false);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.between('md', 'lg'));
  const drawerWidth = isMobile ? 260 : isTablet ? 280 : 300;

  // Initialize routes on app startup
  useEffect(() => {
    const initRoutes = async () => {
      try {
        const { initializeRoutes } = await import('./routing/routeConfig');
        initializeRoutes();
        setRoutesInitialized(true);
        console.log('✅ Routes initialized successfully');
      } catch (error) {
        console.error('❌ Failed to initialize routes:', error);
        setRoutesInitialized(true); // Set to true anyway to prevent infinite loading
      }
    };

    initRoutes();

    // Initialize Firefox content blocking detection
    initializeFirefoxContentBlockingDetection();
  }, []);

  // Auto-close sidebar on mobile
  useEffect(() => {
    if (isMobile) {
      setOpen(false);
    } else {
      setOpen(true);
    }
  }, [isMobile]);

  // Centralized function to handle navigation state changes
  const processNavigation = React.useCallback((page: string, subPage: string) => {
    setLoading(true);
    setActivePage(page);
    setActiveSubPage(subPage);
    if (isMobile) {
      setOpen(false); // Close sidebar on mobile during navigation
    }
    // Simulate loading delay
    setTimeout(() => {
      setLoading(false);
    }, 300);
  }, [isMobile]); // setLoading, setActivePage, setActiveSubPage, setOpen are stable setters

  // Handle URL hash changes for navigation - only after routes are initialized
  useEffect(() => {
    if (!routesInitialized) {
      console.log('⏳ Waiting for routes to initialize before parsing hash...');
      return;
    }

    const handleHashChange = () => {
      const currentHash = window.location.hash.slice(1); // e.g., "/app/publication-ready/table1" or "home"
      console.log('Hash changed:', currentHash); // Debug log
      const pathSegments = currentHash.split('/').filter(Boolean); // e.g., ["app", "publication-ready", "table1"] or ["home"]

      let pageFromHash = 'home';
      let subPageFromHash = '';

      if (pathSegments.length > 0) {
        if (pathSegments[0] === 'app' && pathSegments.length > 1) {
          // Handle /app/* routes
          pageFromHash = pathSegments[1]; // e.g., "publication-ready"
          subPageFromHash = pathSegments[2] || ''; // e.g., "table1" or ""
        } else {
          // Handle top-level routes like /home, /auth, etc.
          pageFromHash = pathSegments[0]; // e.g., "home"
          subPageFromHash = pathSegments[1] || ''; // e.g., ""
        }
      }
      console.log('Parsed pageFromHash:', pageFromHash, 'subPageFromHash:', subPageFromHash); // Debug log

      processNavigation(pageFromHash, subPageFromHash);
    };

    // Initial page setting based on current hash
    handleHashChange();

    // Listen for hash changes
    window.addEventListener('hashchange', handleHashChange);

    // Cleanup
    return () => {
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, [routesInitialized, processNavigation]); // Wait for routes to be initialized

  // Update the navigateToPage function to work with /app prefix
  const navigateToPage = React.useCallback((path: string) => {
  // The 'path' parameter is expected to be the fragment itself, without a leading '#'
  // e.g., "home", "data-management/import", "inference/ttest/onesample"
  const cleanPath = path.startsWith('/') ? path.substring(1) : path;
  const newPath = `#${cleanPath}`; // Prepend only #
  console.log('navigateToPage called with path:', path, 'newHash:', newPath); // Debug log

  if (window.location.hash !== newPath) {
    window.location.hash = newPath; // Update hash for navigation
  } else {
    // If path is the same, force re-evaluation
    const pathSegments = cleanPath.split('/').filter(Boolean);
    const targetPage = pathSegments[0] || 'home';
    const targetSubPage = pathSegments[1] || '';
    console.log('Hash is already the same, forcing navigation to:', targetPage, targetSubPage); // Debug log
    processNavigation(targetPage, targetSubPage);
  }
  }, [processNavigation]);

  // Memoized auth success callback
  const handleAuthSuccess = useCallback(() => {
    navigateToPage('dashboard');
  }, [navigateToPage]);

  // Show notification (memoized with useCallback)
  const showNotification = useCallback((
    message: string,
    severity: 'success' | 'info' | 'warning' | 'error' = 'info',
    actions?: ReactNode,
    autoHideDuration = 6000
  ) => {
    setNotification({
      open: true,
      message,
      severity,
      actions
    });
  }, []); // No dependencies, as setNotification is a stable setter

  // Handle notification close
  const handleNotificationClose = React.useCallback((event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    setNotification(prev => ({ ...prev, open: false }));
    // Always attempt to clear the data load event when the notification related to it is closed.
    // clearDataLoadEvent itself should handle if there's nothing to clear.
    clearDataLoadEvent();
  }, [clearDataLoadEvent]); // Dependency only on clearDataLoadEvent

  const handleDrawerToggle = () => {
    setOpen(!open);
  };

  const handleMenuItemClick = (path: string) => {
    // setLoading(true); // setLoading is handled by navigateToPage
    navigateToPage(path); // Use the new unified function

    // Simulate loading delay
    // setTimeout(() => { // setTimeout is handled by navigateToPage
    //   setLoading(false);
    // }, 300);
  };

  // Note: Authentication and route access control is now handled by AppRouter and route guards

  // Effect to show signup success message
  useEffect(() => {
    if (showSignupSuccessMessage) {
      showNotification(
        "You have successfully confirmed your registration! Enjoy using DataStatPro.",
        "success"
      );
      clearSignupSuccessMessage(); // Clear the flag after showing the message
    }
  }, [showSignupSuccessMessage, showNotification, clearSignupSuccessMessage]);

  // Effect to handle data load notifications
  useEffect(() => {
    if (dataLoadEventInfo) {
      const actions = (
        <Box sx={{ mt: 1 }}>
          <Button
            variant="outlined"
            size="small"
            onClick={() => {
              navigateToPage('data-management/editor'); // Assuming 'data-management/editor' shows the current dataset
              handleNotificationClose(); // This will also call clearDataLoadEvent
            }}
            sx={{ mr: 1, color: 'common.white', borderColor: 'common.white' }}
          >
            View/Edit Data
          </Button>
          <Button
            variant="outlined"
            size="small"
            onClick={() => {
              navigateToPage('analysis-index'); // Changed from 'assistant' to 'stats'
              handleNotificationClose(); // This will also call clearDataLoadEvent
            }}
            sx={{ color: 'common.white', borderColor: 'common.white' }}
          >
            Start Analysis
          </Button>
        </Box>
      );
      showNotification(
        `Data '${dataLoadEventInfo.datasetName}' loaded successfully!`,
        'success',
        actions // Remove the hardcoded duration argument
      );
    }
  }, [dataLoadEventInfo, navigateToPage, handleNotificationClose]);

  // Render the appropriate component based on the active page
  const renderContent = () => {
    if (loading) {
      return (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            width: '100%'
          }}
        >
          <CircularProgress />
        </Box>
      );
    }

    // Use the new AppRouter instead of the switch statement
    return (
      <AppRouter
        activePage={activePage}
        activeSubPage={activeSubPage}
        onNavigate={navigateToPage}
      />
    );

    /* LEGACY SWITCH STATEMENT - KEEPING FOR REFERENCE DURING MIGRATION
    switch (activePage) {
      case 'data-management': // Updated case
        if (activeSubPage === '') {
          // Render DataManagementOptions when no subpage is specified
          return <DataManagementPage onNavigate={navigateToPage} />;
        }
        // Render the DataManagement component for specific tabs
        return <DataManagement initialTab={activeSubPage} onNavigate={navigateToPage} />;
      case 'stats':
        if (activeSubPage === '') {
          return <DescriptiveStatsPage onNavigate={navigateToPage} />;
        }
        return <DescriptiveStats initialTab={activeSubPage} />;
      case 'inference': // Old 'inference' routes
        if (activeSubPage === 'workflow') {
          return <TTestWorkflowPage />;
        } else if (activeSubPage === 'anovaworkflow') {
          return <ANOVAWorkflowPage />;
        } else if (activeSubPage === 'anova') {
          return <ANOVA />;
        } else if (activeSubPage === 'repeated-measures-anova') { // Add route for Repeated Measures ANOVA
          return <RepeatedMeasuresANOVAWorkflowPage />;
        }
        // Fallback for 'inference' if no specific subpage matches (e.g., the old InferentialStats component)
        return <InferentialStats initialTab={activeSubPage} />;
      case 'inferential-stats': // New 'inferential-stats' routes
        // Pass activeSubPage to InferentialStatsPage to handle category filtering
        if (activeSubPage === '') {
           return <InferentialStatsPage onNavigate={navigateToPage} initialSubPage={activeSubPage} />;
        } else if (activeSubPage === 'one-sample-ttest') {
           return <TTests initialTab="oneSample" />; // Use TTests component with initialTab
        } else if (activeSubPage === 'independent-samples-ttest') {
           return <TTests initialTab="independent" />; // Use TTests component with initialTab
        } else if (activeSubPage === 'paired-samples-ttest') {
           return <TTests initialTab="paired" />; // Use TTests component with initialTab
        } else if (activeSubPage === 'mann-whitney-u-test') {
           return <NonParametricTests />; // Assuming NonParametricTests doesn't need onNavigate
        } else if (activeSubPage === 'wilcoxon-signed-rank-test') {
           return <NonParametricTests />; // Assuming NonParametricTests doesn't need onNavigate
        } else if (activeSubPage === 'kruskal-wallis-test') {
           return <NonParametricTests />; // Assuming NonParametricTests doesn't need onNavigate
        } else if (activeSubPage === 'friedman-test') {
           return <NonParametricTests />; // Assuming NonParametricTests doesn't need onNavigate
        } else if (activeSubPage === 'chi-square-test') {
           return <NonParametricTests />; // Assuming NonParametricTests doesn't need onNavigate
        } else if (activeSubPage === 'one-way-anova') {
           return <OneWayANOVA />; // Assuming OneWayANOVA doesn't need onNavigate
        } else if (activeSubPage === 'repeated-measures-anova') {
           return <RepeatedMeasuresANOVA />; // Assuming RepeatedMeasuresANOVA doesn't need onNavigate
        } else if (activeSubPage === 'two-way-anova') {
           return <TwoWayANOVA />; // Assuming TwoWayANOVA doesn't need onNavigate
        }
        // Fallback for 'inferential-stats' if subpage is unknown, still pass the subpage
        return <InferentialStatsPage onNavigate={navigateToPage} initialSubPage={activeSubPage} />;
      case 'correlation': // Keep this case for sub-pages
        return <CorrelationAnalysis initialTab={activeSubPage} />;
      case 'charts':
        if (activeSubPage === '') {
          return <DataVisualizationPage onNavigate={navigateToPage} />;
        } else {
          return <Visualization initialTab={activeSubPage} />;
        }
      case 'pivot':
        // The subPage ('tables' or 'charts') might not be strictly necessary anymore
        // if PivotAnalysisViewer handles both via its internal renderer selection.
        // For now, we just route to the single component.
        return <PivotAnalysisViewer />;
      case 'assistant':
        return <AnalysisAssistantPage onNavigate={handleMenuItemClick} />;
      // Results Manager removed
      case 'whichtest': // Added case for WhichTestPage
        return <WhichTestPage onNavigate={navigateToPage} />;
      case 'visualizationguide': // Added case for VisualizationGuidePage
        return <VisualizationGuidePage />;
      case 'statisticalmethods': // Renamed case for StatisticalMethodsPage
        return <StatisticalMethodsPage />;
      case 'profile':
        return <UserProfile />;
      case 'settings':
        return <Settings />;
      case 'video-tutorials': // Add route for Video Tutorials page
        return <VideoTutorialsPage />;
      case 'samplesize': // Add case for Sample Size Calculator section
        if (activeSubPage === '') {
          return <SampleSizeCalculatorsOptions onNavigate={navigateToPage} />;
        } else if (activeSubPage === 'one-sample') {
          return <OneSampleCalculator />;
        } else if (activeSubPage === 'two-sample') {
          return <TwoSampleCalculator />;
        } else if (activeSubPage === 'paired-sample') {
          return <PairedSampleCalculator />;
        } else if (activeSubPage === 'more-than-two-groups') {
          return <MoreThanTwoGroupsCalculator />;
        }
        // Default to SampleSizeCalculatorsOptions if an unknown subpage is specified
        return <SampleSizeCalculatorsOptions onNavigate={navigateToPage} />;
      case 'epicalc': // Add case for Epidemiological Calculators section
        if (activeSubPage === '') {
          return <EpiCalcPage onNavigate={navigateToPage} />;
        } else if (activeSubPage === 'case-control') {
          return <CaseControlCalculator />;
        } else if (activeSubPage === 'cohort') {
          return <CohortCalculator />;
        } else if (activeSubPage === 'cross-sectional') {
          return <CrossSectionalCalculator />;
        } else if (activeSubPage === 'matched-case-control') {
          return <MatchedCaseControlCalculator />;
        } else if (activeSubPage === 'sample-size-power') {
          return <SampleSizePowerCalculator />;
        }
        // Default to EpiCalcPage if an unknown subpage is specified
        return <EpiCalcPage onNavigate={navigateToPage} />;
      case 'advanced': // Add case for Advanced Analysis section
        if (activeSubPage === 'survival') {
          return <SurvivalAnalysis />;
        } else if (activeSubPage === 'reliability') {
          return <ReliabilityAnalysis />;
        } else if (activeSubPage === 'mediation') {
          return <MediationModeration />;
        } else if (activeSubPage === 'efa' || activeSubPage === 'ExploratoryFactorAnalysis') {
          return <ExploratoryFactorAnalysis />;
        }
        // Default to survival analysis if no subpage specified
        return <SurvivalAnalysis />;
      case 'publication-ready': // Add case for Publication Ready section
        if (activeSubPage === 'table1') {
          return <Table1Page />;
        } else if (activeSubPage === 'table2') { // Add case for Table 2
          return <Table2Page />;
        } else if (activeSubPage === 'table1a') { // Add case for Table 1a
            return <Table1a />;
        } else if (activeSubPage === 'smd-table') { // Add case for SMD Table
            return <SMDTable />;
        } else if (activeSubPage === 'flow-diagram') { // Add case for Flow Diagram
            return <FlowDiagramPage />;
        } else if (activeSubPage === 'regression-table') { // Add case for Regression Table
            return <RegressionTablePage />;
        } else if (activeSubPage === 'convert-to-apa') { // Add case for Convert to APA
            return <ConvertToAPA />;
        } else if (activeSubPage === 'posthoc-tests') { // Add case for PostHoc Tests
            return <PostHocTestsPage />;
        }
        // If no specific subpage matches, render the main PublicationReadyPage
        return <PublicationReadyPage onNavigate={navigateToPage} />;
      case 'advanced-analysis': // Add case for Advanced Analysis section
        if (activeSubPage === '') {
          return <AdvancedAnalysisPage onNavigate={navigateToPage} />;
        } else if (activeSubPage === 'survival') {
          return <SurvivalAnalysis />;
        } else if (activeSubPage === 'reliability') {
          return <ReliabilityAnalysis />;
        } else if (activeSubPage === 'mediation') {
          return <MediationModeration />;
        } else if (activeSubPage === 'efa' || activeSubPage === 'ExploratoryFactorAnalysis') {
          return <ExploratoryFactorAnalysis />;
        } else if (activeSubPage === 'meta-analysis') {
          return <MetaAnalysis />;
        } else if (activeSubPage === 'cluster') {
          return <ClusterAnalysis />;
        } else if (activeSubPage === 'cfa' || activeSubPage === 'ConfirmatoryFactorAnalysis') {
          return <ConfirmatoryFactorAnalysis />;
        }
        // Default to AdvancedAnalysisPage if an unknown subpage is specified
        return <AdvancedAnalysisPage onNavigate={navigateToPage} />;
      case 'correlation-analysis': // Add case for Correlation Analysis section
        if (activeSubPage === '') {
          return <CorrelationAnalysisPage onNavigate={navigateToPage} />;
        } else if (activeSubPage === 'correlation-matrix') {
          return <CorrelationAnalysis />; // Assuming CorrelationAnalysis component handles matrix
        } else if (activeSubPage === 'linear-regression') {
          return <CorrelationAnalysis />; // Assuming CorrelationAnalysis component handles linear regression
        } else if (activeSubPage === 'logistic-regression') {
          return <CorrelationAnalysis />; // Assuming CorrelationAnalysis component handles logistic regression
        }
        // Default to CorrelationAnalysisPage if an unknown subpage is specified
        return <CorrelationAnalysisPage onNavigate={navigateToPage} />;
      case 'reliability': // Add case for Reliability Analysis
        return <ReliabilityAnalysis />;
      case 'survival': // Add case for Survival Analysis page (legacy route)
        return <SurvivalAnalysis />;
      case 'reset-password':
        return <UpdatePassword />;
      case 'auth':
        // Pass the same onAuthSuccess handler which navigates to dashboard
        // This will be triggered for both successful login/signup AND guest login
        return <Auth
                 initialView={activeSubPage === 'register' ? AuthView.REGISTER : AuthView.LOGIN}
                 onAuthSuccess={handleAuthSuccess}
               />;
      case 'analysis-index': // Add case for Analysis Index page
        return <AnalysisIndexPage onNavigate={navigateToPage} />;
      case 'knowledge-base': // Add case for Knowledge Base page
        if (activeSubPage) {
          // If there's a subpage, it's a tutorial page
          return <TutorialPage />;
        }
        return <KnowledgeBasePage />;
      case 'dashboard': // Add dashboard case
      case 'home': // Map home to DashboardPage
      default:
        // Pass handleMenuItemClick as onNavigate prop
        return <DashboardPage onNavigate={navigateToPage} />; // Render DashboardPage and pass navigateToPage
    }
    END OF LEGACY SWITCH STATEMENT */
  };

  // Show loading screen while authentication is being restored or routes are being initialized
  if (authLoading || !routesInitialized) {
    const loadingMessage = authLoading
      ? 'Restoring session...'
      : !routesInitialized
        ? 'Initializing routes...'
        : 'Loading DataStatPro...';

    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          backgroundColor: 'background.default'
        }}
      >
        <CircularProgress size={60} sx={{ mb: 2 }} />
        <Typography variant="h6" color="text.secondary">
          {loadingMessage}
        </Typography>
      </Box>
    );
  }

   return (
     <>
       {/* Render AppHeader outside the main flex container */}
       {user ? (
           <AuthAppHeader
             title="DataStatPro"
             onMenuClick={handleDrawerToggle}
             onNavigate={navigateToPage} // Pass the new navigation handler
           />
         ) : (
           <AppHeader
             title="DataStatPro"
             onMenuClick={handleDrawerToggle}
             onNavigate={navigateToPage}
             isGuest={isGuest} // Pass isGuest state
             logoutGuest={logoutGuest} // Pass logoutGuest function
          />
         )}
         {/* Create a new flex container specifically for Sidebar and MainContent */}
         <Box sx={{ position: 'relative', height: 'calc(100vh - 64px)' /* Adjust height for header */ }}>
         <Sidebar
            open={open}
            drawerWidth={drawerWidth}
            onClose={handleDrawerToggle}
            onMenuItemClick={handleMenuItemClick}
            activePage={activePage}
            activeSubPage={activeSubPage}
            isGuest={isGuest} // Pass guest status to Sidebar
          />
          <MainContent drawerWidth={drawerWidth} open={open}>
            <Suspense fallback={<Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', width: '100%' }}><CircularProgress /></Box>}>
              {renderContent()}
            </Suspense>
          </MainContent>

        {/* Notification Snackbar */}
        <Snackbar
          open={notification.open}
          autoHideDuration={notification.actions ? 3000 : 6000} // 3 seconds for data load msg (has actions), 6 seconds otherwise
          onClose={handleNotificationClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            onClose={handleNotificationClose}
            severity={notification.severity}
            elevation={6}
            variant="filled"
            sx={{ width: '100%' }} // Ensure alert takes full width of snackbar
          >
            {notification.message}
            {notification.actions && <Box sx={{ pt: 1 }}>{notification.actions}</Box>}
          </Alert>
        </Snackbar>

        {/* Enhanced PWA Update Notification */}
        <UpdateNotification anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }} />

        {/* Responsive Design Tester (only shown in development) */}
        {isDevelopment && <ResponsiveTester />}

        {/* Dataset Name Conflict Dialog (rendered globally) */}
        <DatasetNameConflictDialog />
        </Box>
    </>
  );
}

export default App;
