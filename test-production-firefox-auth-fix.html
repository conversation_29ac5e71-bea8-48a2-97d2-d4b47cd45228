<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Production Firefox Authentication Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.warning { background-color: #fff3cd; color: #856404; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .log-container {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .test-section {
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🦊 Production Firefox Authentication Fix Test</h1>
        <p>This test verifies the production Firefox authentication fix functionality.</p>
        
        <div class="status info">
            <strong>Environment:</strong> <span id="environment">Detecting...</span><br>
            <strong>Browser:</strong> <span id="browser">Detecting...</span><br>
            <strong>Fix Active:</strong> <span id="fixActive">Checking...</span>
        </div>
    </div>

    <div class="container test-section">
        <h2>🔧 Environment Detection Test</h2>
        <button onclick="testEnvironmentDetection()">Test Environment Detection</button>
        <div id="envTestResult" class="status" style="display: none;"></div>
        <div id="envDebugInfo" class="debug-info" style="display: none;"></div>
    </div>

    <div class="container test-section">
        <h2>🔒 Authentication Protection Test</h2>
        <button onclick="testAuthenticationProtection()">Start Authentication Protection Test</button>
        <button onclick="endAuthenticationProtection()" disabled id="endProtectionBtn">End Protection</button>
        <div id="authTestResult" class="status" style="display: none;"></div>
        <div id="authDebugInfo" class="debug-info" style="display: none;"></div>
    </div>

    <div class="container test-section">
        <h2>🚫 Service Worker Blocking Test</h2>
        <button onclick="testServiceWorkerBlocking()">Test Service Worker Blocking</button>
        <div id="swTestResult" class="status" style="display: none;"></div>
        <div id="swDebugInfo" class="debug-info" style="display: none;"></div>
    </div>

    <div class="container test-section">
        <h2>🌐 Fetch Interception Test</h2>
        <button onclick="testFetchInterception()">Test Fetch Interception</button>
        <div id="fetchTestResult" class="status" style="display: none;"></div>
        <div id="fetchDebugInfo" class="debug-info" style="display: none;"></div>
    </div>

    <div class="container test-section">
        <h2>📊 Real-time Debug Information</h2>
        <button onclick="refreshDebugInfo()">Refresh Debug Info</button>
        <button onclick="toggleAutoRefresh()" id="autoRefreshBtn">Start Auto-Refresh</button>
        <div id="debugInfo" class="debug-info">Click "Refresh Debug Info" to see current state...</div>
    </div>

    <div class="container">
        <h2>📝 Test Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="testLog" class="log-container"></div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let testLog = [];

        // Mock production Firefox auth fix for testing
        const mockProductionFirefoxAuthFix = {
            isFirefox: /Firefox/.test(navigator.userAgent),
            isProduction: window.location.protocol === 'https:' ||
                         window.location.hostname.includes('datastatpro'),
            authenticationLock: false,
            authFlags: {},
            
            initialize() {
                this.log('🦊 Mock Production Firefox Auth Fix initialized');
                return true;
            },
            
            startAuthenticationProtection() {
                this.authenticationLock = true;
                localStorage.setItem('firefox-production-auth-lock', 'true');
                localStorage.setItem('firefox-production-auth-start', Date.now().toString());
                this.log('🦊 Authentication protection started');
            },
            
            endAuthenticationProtection() {
                this.authenticationLock = false;
                localStorage.removeItem('firefox-production-auth-lock');
                localStorage.removeItem('firefox-production-auth-start');
                this.log('🦊 Authentication protection ended');
            },
            
            signalAuthenticationSuccess() {
                localStorage.setItem('firefox-production-auth-success', 'true');
                this.log('🦊 Authentication success signaled');
                setTimeout(() => {
                    localStorage.removeItem('firefox-production-auth-success');
                }, 2000);
            },
            
            signalAuthenticationFailure() {
                localStorage.setItem('firefox-production-auth-failure', 'true');
                this.log('🦊 Authentication failure signaled');
                setTimeout(() => {
                    localStorage.removeItem('firefox-production-auth-failure');
                }, 2000);
            },
            
            getDebugInfo() {
                return {
                    isFirefox: this.isFirefox,
                    isProduction: this.isProduction,
                    authenticationLock: this.authenticationLock,
                    authFlags: {
                        'firefox-production-auth-lock': localStorage.getItem('firefox-production-auth-lock'),
                        'firefox-production-auth-start': localStorage.getItem('firefox-production-auth-start'),
                        'firefox-production-auth-success': localStorage.getItem('firefox-production-auth-success'),
                        'firefox-production-auth-failure': localStorage.getItem('firefox-production-auth-failure')
                    },
                    timestamp: new Date().toISOString()
                };
            },
            
            log(message) {
                console.log(message);
                addToLog(message);
            }
        };

        // Initialize on page load
        window.addEventListener('load', () => {
            updateEnvironmentInfo();
            mockProductionFirefoxAuthFix.initialize();
        });

        function updateEnvironmentInfo() {
            const isFirefox = /Firefox/.test(navigator.userAgent);
            const isProduction = window.location.protocol === 'https:' || 
                               window.location.hostname.includes('datastatpro');
            
            document.getElementById('environment').textContent = isProduction ? 'Production' : 'Development';
            document.getElementById('browser').textContent = isFirefox ? 'Firefox' : 'Other';
            document.getElementById('fixActive').textContent = isFirefox && isProduction ? 'Yes' : 'No';
        }

        function addToLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            testLog.push(`[${timestamp}] ${message}`);
            updateLogDisplay();
        }

        function updateLogDisplay() {
            const logContainer = document.getElementById('testLog');
            logContainer.innerHTML = testLog.slice(-50).join('\n');
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            testLog = [];
            updateLogDisplay();
        }

        function showResult(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }

        function showDebugInfo(elementId, info) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(info, null, 2);
            element.style.display = 'block';
        }

        function testEnvironmentDetection() {
            addToLog('🔧 Starting environment detection test...');
            
            const debugInfo = {
                userAgent: navigator.userAgent,
                protocol: window.location.protocol,
                hostname: window.location.hostname,
                isFirefox: /Firefox/.test(navigator.userAgent),
                isProduction: window.location.protocol === 'https:' || 
                             window.location.hostname.includes('datastatpro'),
                importMeta: 'Not available in browser context'
            };
            
            showDebugInfo('envDebugInfo', debugInfo);
            
            if (debugInfo.isFirefox && debugInfo.isProduction) {
                showResult('envTestResult', 'success', '✅ Environment detection successful - Firefox in production detected');
                addToLog('✅ Environment detection test passed');
            } else if (debugInfo.isFirefox) {
                showResult('envTestResult', 'warning', '⚠️ Firefox detected but not in production environment');
                addToLog('⚠️ Environment detection test - Firefox but not production');
            } else {
                showResult('envTestResult', 'info', 'ℹ️ Not Firefox - fix would not be active');
                addToLog('ℹ️ Environment detection test - Not Firefox');
            }
        }

        function testAuthenticationProtection() {
            addToLog('🔒 Starting authentication protection test...');
            
            try {
                mockProductionFirefoxAuthFix.startAuthenticationProtection();
                
                const debugInfo = mockProductionFirefoxAuthFix.getDebugInfo();
                showDebugInfo('authDebugInfo', debugInfo);
                
                if (debugInfo.authenticationLock) {
                    showResult('authTestResult', 'success', '✅ Authentication protection activated successfully');
                    document.getElementById('endProtectionBtn').disabled = false;
                    addToLog('✅ Authentication protection test - protection activated');
                } else {
                    showResult('authTestResult', 'error', '❌ Authentication protection failed to activate');
                    addToLog('❌ Authentication protection test failed');
                }
            } catch (error) {
                showResult('authTestResult', 'error', `❌ Authentication protection test error: ${error.message}`);
                addToLog(`❌ Authentication protection test error: ${error.message}`);
            }
        }

        function endAuthenticationProtection() {
            addToLog('🔒 Ending authentication protection...');
            
            try {
                mockProductionFirefoxAuthFix.endAuthenticationProtection();
                
                const debugInfo = mockProductionFirefoxAuthFix.getDebugInfo();
                showDebugInfo('authDebugInfo', debugInfo);
                
                if (!debugInfo.authenticationLock) {
                    showResult('authTestResult', 'success', '✅ Authentication protection ended successfully');
                    document.getElementById('endProtectionBtn').disabled = true;
                    addToLog('✅ Authentication protection ended');
                } else {
                    showResult('authTestResult', 'error', '❌ Authentication protection failed to end');
                    addToLog('❌ Failed to end authentication protection');
                }
            } catch (error) {
                showResult('authTestResult', 'error', `❌ End protection error: ${error.message}`);
                addToLog(`❌ End protection error: ${error.message}`);
            }
        }

        function testServiceWorkerBlocking() {
            addToLog('🚫 Starting service worker blocking test...');
            
            if (!('serviceWorker' in navigator)) {
                showResult('swTestResult', 'warning', '⚠️ Service Worker not supported in this browser');
                addToLog('⚠️ Service Worker not supported');
                return;
            }
            
            // Start protection first
            mockProductionFirefoxAuthFix.startAuthenticationProtection();
            
            // Try to register a service worker (should be blocked)
            navigator.serviceWorker.register('/test-sw.js')
                .then(registration => {
                    const debugInfo = {
                        registrationScope: registration.scope,
                        authLock: mockProductionFirefoxAuthFix.authenticationLock,
                        blocked: registration.scope === '/' && !registration.active
                    };
                    
                    showDebugInfo('swDebugInfo', debugInfo);
                    
                    if (debugInfo.blocked) {
                        showResult('swTestResult', 'success', '✅ Service worker registration blocked successfully');
                        addToLog('✅ Service worker blocking test passed');
                    } else {
                        showResult('swTestResult', 'warning', '⚠️ Service worker registration not blocked (may be expected)');
                        addToLog('⚠️ Service worker not blocked');
                    }
                })
                .catch(error => {
                    showResult('swTestResult', 'success', '✅ Service worker registration blocked (threw error as expected)');
                    showDebugInfo('swDebugInfo', { error: error.message, authLock: mockProductionFirefoxAuthFix.authenticationLock });
                    addToLog('✅ Service worker blocking test passed (error thrown)');
                })
                .finally(() => {
                    // End protection
                    mockProductionFirefoxAuthFix.endAuthenticationProtection();
                });
        }

        function testFetchInterception() {
            addToLog('🌐 Starting fetch interception test...');
            
            // Start protection
            mockProductionFirefoxAuthFix.startAuthenticationProtection();
            
            // Test fetch to a Supabase-like URL
            const testUrl = 'https://test.supabase.co/auth/v1/token';
            
            fetch(testUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ test: true })
            })
            .then(response => {
                const debugInfo = {
                    url: testUrl,
                    status: response.status,
                    headers: Object.fromEntries(response.headers.entries()),
                    authLock: mockProductionFirefoxAuthFix.authenticationLock
                };
                
                showDebugInfo('fetchDebugInfo', debugInfo);
                showResult('fetchTestResult', 'info', 'ℹ️ Fetch request completed (interception may not be visible in test)');
                addToLog('ℹ️ Fetch interception test completed');
            })
            .catch(error => {
                const debugInfo = {
                    url: testUrl,
                    error: error.message,
                    authLock: mockProductionFirefoxAuthFix.authenticationLock
                };
                
                showDebugInfo('fetchDebugInfo', debugInfo);
                showResult('fetchTestResult', 'warning', '⚠️ Fetch request failed (expected for test URL)');
                addToLog('⚠️ Fetch test failed (expected)');
            })
            .finally(() => {
                // End protection
                mockProductionFirefoxAuthFix.endAuthenticationProtection();
            });
        }

        function refreshDebugInfo() {
            const debugInfo = mockProductionFirefoxAuthFix.getDebugInfo();
            document.getElementById('debugInfo').textContent = JSON.stringify(debugInfo, null, 2);
            addToLog('📊 Debug info refreshed');
        }

        function toggleAutoRefresh() {
            const btn = document.getElementById('autoRefreshBtn');
            
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                btn.textContent = 'Start Auto-Refresh';
                addToLog('📊 Auto-refresh stopped');
            } else {
                autoRefreshInterval = setInterval(refreshDebugInfo, 2000);
                btn.textContent = 'Stop Auto-Refresh';
                addToLog('📊 Auto-refresh started (2s interval)');
            }
        }

        // Initial debug info load
        setTimeout(refreshDebugInfo, 1000);
    </script>
</body>
</html>